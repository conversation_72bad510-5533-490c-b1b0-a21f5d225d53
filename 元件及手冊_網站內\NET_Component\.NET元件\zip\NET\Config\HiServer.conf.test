# IP is used to indicate an alternative ip number to post http or https
# data for required transactions. Domain name is OK, too.
IP=www.focas-api-test.fisc.com.tw

# Port is used to indicate an alternative port number to post https
# data for required transactions. This setting is 443 if the data is
# being passed with HTTPS.
Port=443

# The URL that will be sent to the Server on Authorization Transaction required. 
TrxUrl=/FOCAS_API/request/send.do

# The URL that will be sent to the Server on Getting Key Version required 
KeyUrl=/FOCAS_API/request/change.do

SmartPayUrl=https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/debit/
UPOPUrl=https://www.focas-test.fisc.com.tw/FOCAS_UPOP/upop/

timeOut=60000
readwritetimeout=60000
pubkeytempfilename=

