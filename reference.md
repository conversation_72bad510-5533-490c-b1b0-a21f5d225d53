





財金資訊股份有限公司
跨境匯入網路電子支付交易規格書







版本：Vv1.01
20186 年 063 月 017 日 
版權聲明
此文件為財金資訊股份有限公司所有，非經本公司之同意或授權不得重製、改作、散布或為其他侵權行為。
 
版本記錄
版本	日期	備註
1.0	2016/06/01	初版
1.1	2018/03/07	欄位21「附加資訊」增加註記說明。

 
目錄
1	規格說明	6
1.1	格式說明	6
1.2	欄位名稱	6
1.3	Web模式收送電文結構	8
2	Online交易電文說明	9
2.1	跨境匯入網路電文交易說明	9
2.1.1	跨境匯入網路電子支付交易	9
2.1.2	跨境匯入網路電子支付退款交易	11
2.1.3	跨境匯入網路電子支付訂單狀態查詢交易	13
2.2	驗證碼更新交易	14
3	欄位說明	15
3.1	FIELD_01 交易狀態	15
3.2	FIELD_02 交易類型	15
3.3	FIELD_03 平台代號	15
3.4	FIELD_04 訂單編號	15
3.5	FIELD_05收單行代碼	16
3.6	FIELD_06 特店代號	16
3.7	FIELD_07 端末代號	16
3.8	FIELD_08 交易金額	16
3.9	FIELD_09 交易日期	16
3.10	FIELD_10 交易時間	17
3.11	FIELD_11 商品名稱	17
3.12	FIELD_12 商品描述	17
3.13	FIELD_13 退款編號	17
3.14	FIELD_14 退款金額	17
3.15	FIELD_15 退款原因	17
3.16	FIELD_16交易結果回傳網址	18
3.17	FIELD_17 交易處理回應時間	18
3.18	FIELD_18 REQ交易驗證	18
3.19	FIELD_19 RESP交易驗證	18
3.20	FIELD_20回應碼	18
3.21	FIELD_21 附加資訊	18
3.22	FIELD_22驗證碼押碼方式	19
3.23	FIELD_23 交易追蹤號	20
3.24	FIELD_24 錯誤描述	20
4	其他注意事項	21
附錄一：回應碼	22
 
1	規格說明
1.1	格式說明
格式	說明
A	純文字格式。
N	純數值格式。
AN	文數字格式。
S	特殊字元，如\ / : * ? " < > | % $ & ’ @ +。
ANS	文數字及特殊字元格式。
H	Binary資料轉換成Hex字元0~9、A~F。

1.2	欄位名稱
交易傳送過程中使用『系統參數名稱』當參數名，請區分大小寫；『欄位中文名稱』僅提供參考，詳細內容說明詳見各交易賦予之定義。
#	系統參數名稱	欄位中文名稱	Fmt	Len
01	txnStatus	交易狀態	AN	1
02	txnType	交易類型	AN	4
03	platformNo	平台代號	AN	10
04	orderNumber	訂單編號	AN	19
05	acqBank	收單行代碼	N	3
06	merchantId	特店代號	AN	15
07	terminalId	端末代號	AN	8
08	purchAmt	交易金額	N	10
09	LocalDate	交易日期	N	8
10	LocalTime	交易時間	N	6
11	itemName	商品名稱	AN	256
12	itemDesc	商品描述	AN	400
13	refundNumber	退款編號	AN	19
14	refundAmt	退款金額	N	10
15	refundReason	退款原因	AN	100
16	AuthResURL	交易結果回傳網址	ANS	512
17	transRespTime	交易處理回應時間	N	14
18	reqSignature	REQ交易驗證	H	64
19	respSignature	RESP交易驗證	H	64
20	responseCode	回應碼	N	4
21	otherInfo	附加資訊	ANS	999
22	signatureType	驗證碼押碼方式	N	1
23	rrn	交易追蹤號	N	12
24	errDesc	錯誤描述	ANS	512
註：欄位編號僅用來查詢欄位說明時的順序，與交易無任何關係，其中Fmt表示欄位格式，Len表示欄位可允許之最大長度，若有特殊格式需固定長度的部分將描述於欄位說明章節。

 
1.3	Web模式收送電文結構
跨境匯入網路電子支付交易連結網址：
	測試環境
https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/InBoundEc/
	營運環境
https://www.focas.fisc.com.tw/FOCAS_WEBPOS/InBoundEc/

REQ	RESP
Header
POST / HTTP/1.1
Content-Type: application/x-www-form-urlencoded
Content-Length: xxx (依據context長度給予正確值)
Context
所有參數的字元編碼charset固定採用UTF-8，使用POST模式透過Internet http傳送相關資料參數，詳見下列規格。
 
2	Online交易電文說明
2.1	跨境匯入網路電文交易說明
2.1.1	跨境匯入網路電子支付交易
交易流程架構說明如后
 

付款交易請求回應規格(網路特店收單系統-財金共用收單系統)
#	系統參數名稱	欄位中文名稱	Fmt	Len	REQ	RESP	備註
01	txnStatus	交易狀態	AN	1	M	M	
02	txnType	交易類型	AN	4	M	M	限「0001」
03	platformNo	平台代號	AN	10	M	M	
04	orderNumber	訂單編號	AN	19	M	M	必須唯一識別值
05	acqBank	收單行代碼	N	3	M	M	
06	merchantId	特店代號	AN	15	M	M	
07	terminalId	端末代號	AN	8	M	M	
08	purchAmt	交易金額	N	10	M	M	
09	LocalDate	交易日期	N	8	M	M	yyyyMMdd (GMT+8)
10	LocalTime	交易時間	N	6	M	M	HHmmss(GMT+8)
11	itemName	商品名稱	AN	256	M		
12	itemDesc	商品描述	AN	400	O		
16	AuthResURL	交易結果回傳網址	ANS	512	M		
17	transRespTime	交易處理回應時間	N	14		M	yyyyMMddHHmmss
(GMT+8)
18	reqSignature	REQ交易驗證	H	64	M		SHA-256(平台代號&訂單編號&收單行代碼&特店代號&端末代號&交易金額&交易日期&交易時間&驗證參數註一)
19	respSignature	RESP交易驗證	H	64		M	SHA-256(平台代號&訂單編號&收單行代碼&特店代號&端末代號&回應碼&交易處理回應時間&驗證參數註一)
20	responseCode	回應碼	N	4		M	詳見附錄一
21	otherInfo	附加資訊	ANS	999	C	C	詳見欄位說明
22	signatureType	驗證碼押碼方式	N	1	M	M	
24	errDesc	錯誤描述	ANS	512		M	若交易成功為空值
M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位
註一：財金共用收單系統將提供初始化驗證參數，需由網路特店收單系統透過「驗證碼更新交易」進行更新。
 
2.1.2	跨境匯入網路電子支付退款交易
交易流程架構說明如后
 

退款交易請求回應規格(網路特店收單系統-財金共用收單系統)，本交易支援多次部份退款。
#	系統參數名稱	欄位中文名稱	Fmt	Len	REQ	RESP	備註
01	txnStatus	交易狀態	AN	1	M	M	
02	txnType	交易類型	AN	4	M	M	限「1001」
03	platformNo	平台代號	AN	10	M	M	需同原交易
04	orderNumber	訂單編號	AN	19	M	M	需同原交易
05	acqBank	收單行代碼	N	3	M	M	需同原交易
06	merchantId	特店代號	AN	15	M	M	需同原交易
07	terminalId	端末代號	AN	8	M	M	需同原交易
09	LocalDate	退款日期	N	8	M	M	yyyyMMdd (GMT+8)
10	LocalTime	退款時間	N	6	M	M	HHmmss(GMT+8)
13	refundNumber	退款編號	AN	19	M	M	必須唯一識別值
14	refundAmt	退款金額	N	10	M	M	
15	refundReason	退款原因	AN	100	M		
16	AuthResURL	交易結果回傳網址	ANS	512	M		
17	transRespTime	交易處理回應時間	N	14		M	yyyyMMddHHmmss
(GMT+8)
18	reqSignature	REQ交易驗證	H	64	M		SHA-256(平台代號&訂單編號&收單行代碼&特店代號&端末代號&退款編號&退款金額&退款日期&退款時間&驗證參數)
19	respSignature	RESP交易驗證	H	64		M	SHA-256(平台代號&訂單編號&收單行代碼&特店代號&端末代號&退款編號&回應碼&交易處理回應時間&驗證參數)
20	responseCode	回應碼	N	4		M	詳見附錄一
21	otherInfo	附加資訊	ANS	999	C	C	詳見欄位說明
22	signatureType	驗證碼押碼方式	N	1	M	M	
24	errDesc	錯誤描述	ANS	512		M	若交易成功為空值
M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位
 
2.1.3	跨境匯入網路電子支付訂單狀態查詢交易
 

訂單狀態查詢交易請求回應規格(網路特店收單系統-財金共用收單系統)
#	系統參數名稱	欄位中文名稱	Fmt	Len	REQ	RESP	備註
01	txnStatus	交易狀態	AN	1	M	M	
02	txnType	交易類型	AN	4	M	M	限「9000」
03	platformNo	平台代號	AN	10	M	M	需同原交易
04	orderNumber	訂單編號	AN	19	M	M	需同原交易
05	acqBank	收單行代碼	N	3	M	M	需同原交易
06	merchantId	特店代號	AN	15	M	M	需同原交易
07	terminalId	端末代號	AN	8	M	M	需同原交易
09	LocalDate	交易日期	N	8	M	M	yyyyMMdd (GMT+8)
10	LocalTime	交易時間	N	6	M	M	HHmmss(GMT+8)
13	refundNumber	退款編號	AN	19	C	C	需同原退款交易
16	AuthResURL	交易結果回傳網址	ANS	512	M		
18	reqSignature	REQ交易驗證	H	64	M		SHA-256(平台代號&訂單編號&收單行代碼&特店代號&端末代號&交易日期&交易時間&驗證參數)
19	respSignature	RESP交易驗證	H	64		M	SHA-256(平台代號&訂單編號&收單行代碼&特店代號&端末代號&回應碼&驗證參數)
20	responseCode	回應碼	N	4		M	詳見附錄一
21	otherInfo	附加資訊	ANS	999	C	C	詳見欄位說明
22	signatureType	驗證碼押碼方式	N	1	M	M	
24	errDesc	錯誤描述	ANS	512		M	若交易成功為空值
M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位

 
2.2	驗證碼更新交易
 

驗證碼更新交易請求回應規格(網路特店收單系統-財金共用收單系統)
#	系統參數名稱	欄位中文名稱	Fmt	Len	REQ	RESP	備註
01	txnStatus	交易狀態	AN	1	M	M	
02	txnType	交易類型	AN	4	M	M	限「9001」
05	acqBank	收單行代碼	N	3	M	M	
06	merchantId	特店代號	AN	15	M	M	
07	terminalId	端末代號	AN	8	M	M	
23	rrn	交易追蹤號	N	12	M	M	
09	LocalDate	交易日期	N	8	M	M	yyyyMMdd (GMT+8)
10	LocalTime	交易時間	N	6	M	M	HHmmss(GMT+8)
16	AuthResURL	交易結果回傳網址	ANS	512	M		
18	reqSignature	REQ交易驗證	H	64	M		SHA-256(收單行代碼&特店代號&端末代號&交易追蹤號&交易日期&交易時間&原驗證參數)
19	respSignature	RESP交易驗證	H	64		M	SHA-256(收單行代碼&特店代號&端末代號&交易追蹤號&交易日期&交易時間&新驗證參數)
20	responseCode	回應碼	N	4		M	詳見附錄一
21	otherInfo	附加資訊	ANS	999		M	詳見欄位說明
22	signatureType	驗證碼押碼方式	N	1	M	M	
24	errDesc	錯誤描述	ANS	512		M	若交易成功為空值
M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位
 
3	欄位說明
依照欄位編號詳述各欄位資訊，包含格式、長度和說明等項目，其中長度表示此欄位可允許之最大長度，若有特殊格式需固定長度的部分將描述於各欄位說明。
3.1	FIELD_01 交易狀態
格式：	AN
長度：	1 bytes
說明：	0：交易請求。
1：交易回應。
3.2	FIELD_02 交易類型
格式：	AN
長度：	4 bytes
說明：	
交易類型	說明
0001	付款交易
1001	退款交易
9000	查詢交易
9001	驗證碼更新交易

3.3	FIELD_03 平台代號
格式：	AN
長度：	10 bytes
說明：	由財金公司提供境外機構平台代號，支付寶：0001004156。
3.4	FIELD_04 訂單編號
格式：	AN
長度：	19 bytes
說明：	特店給予之訂單序號，必須是特店唯一識別值，不可重複。
3.5	FIELD_05收單行代碼
格式：	N	
長度：	3 bytes	
說明：	收單銀行代碼。	
3.6	FIELD_06 特店代號
格式：	AN
長度：	15 bytes
說明：	收單銀行特店識別值，建議編碼方式：收單行代碼(3位)+統一編號+(8位)+端末代號(前4位)。
3.7	FIELD_07 端末代號
格式：	AN
長度：	8 bytes
說明：	設備端末代號。
3.8	FIELD_08 交易金額
格式：	N	
長度：	10 bytes	
說明：	台幣整數金額，例如：交易金額為999元，此欄位請放置999。	
3.9	FIELD_09 交易日期
格式：	N
長度：	8 bytes
說明：	付款/退款/查詢/驗證碼更新 交易發生日期，其格式：yyyyMMdd (GMT+8) 。
3.10	FIELD_10 交易時間
格式：	N
長度：	6 bytes
說明：	付款/退款/查詢/驗證碼更新 交易發生時間，其格式：HHmmss (GMT+8) 。
3.11	FIELD_11 商品名稱
格式：	AN
長度：	256 bytes
說明：	購物商品名稱，除文數字及空白之外，不可包含任何特殊符號。
3.12	FIELD_12 商品描述
格式：	AN
長度：	400 bytes
說明：	購物商品描述，除文數字及空白之外，不可包含任何特殊符號。
3.13	FIELD_13 退款編號
格式：	AN
長度：	19 bytes
說明：	特店給予之退款序號，必須是特店唯一識別值，不可重複。
3.14	FIELD_14 退款金額
格式：	N
長度：	10 bytes
說明：	退款金額必須小於等於原交易金額，格式說明詳參FIELD_08交易金額。
3.15	FIELD_15 退款原因
格式：	AN
長度：	100 bytes
說明：	退款原因描述，例如：out of supply，除文數字及空白之外，不可包含任何特殊符號。
3.16	FIELD_16交易結果回傳網址
格式：	ANS	
長度：	512 bytes	
說明：	財金共用收單系統會將交易回應(RESP)訊息以POST方式送到指定網址。	
3.17	FIELD_17 交易處理回應時間
格式：	N
長度：	14 bytes
說明：	財金共用收單系統處理完成回應時間，其格式：yyyyMMddHHmmss (GMT+8) 。
3.18	FIELD_18 REQ交易驗證
格式：	H	
長度：	64 bytes	
說明：	特店交易請求驗證碼。	
3.19	FIELD_19 RESP交易驗證
格式：	H	
長度：	64 bytes	
說明：	財金共用收單系統回應驗證碼。	
3.20	FIELD_20回應碼
格式：	N	
長度：	4 bytes	
說明：	回應內容詳參附錄一。	
3.21	FIELD_21 附加資訊
格式：	ANS
長度：	999 bytes
說明：	此欄位格式採用json，並依不同交易型態提供不同相對應之子欄位內容。
一、	子欄位說明：
#	系統參數名稱	欄位中文名稱	Fmt	Len
01	tag06	新驗證碼資訊	AN	16
02	tag10	新驗證碼檢查值	H	6

(1)	付款交易規格
目前無使用此欄位。
(2)	退款交易規格
目前無使用此欄位。
(3)	查詢交易規格
目前無使用此欄位。
(4)	驗證碼更新交易規格
#	系統參數名稱	欄位中文名稱	Fmt	Len	REQ	RESP	備註
01	tag06	新驗證碼資訊	AN	16		M	1.	接收端需使用舊驗證碼對此欄位進行3DES ECB解密作業，取得交換後的新驗證碼值。
2.	驗證碼長度建議採用32或48。
02	tag10	新驗證碼檢查值	H	6		M	新驗證碼的key check value。

註一：新驗證碼值與舊驗證碼值之長度相同。
註二：新驗證碼檢查值採用新驗證碼對”0000000000000000”(Hex2Byte轉換為8 Bytes資料進行計算)，進行3DES ECB加密作業，並取最後3bytes，經hex轉換，用來驗證解開的新key是否正確。
	註三：3DES ECB加密作業之Padding採用『NoPadding』。
	
3.22	FIELD_22驗證碼押碼方式
格式：	N
長度：	1 bytes
說明：	驗證碼押碼方式。
1：SHA-256
3.23	FIELD_23 交易追蹤號
格式：	N
長度：	12 bytes
說明：	由特店自行定義，並確保其唯一性即可。
建議格式：DDDHHMMSSNNN。
DDD：太陽日
HH：時(24小時制)
MM：分
SS：秒
NNN：流水序號
3.24	FIELD_24 錯誤描述
格式：	ANS
長度：	512 bytes
說明：	交易失敗原因說明。

 
4	其他注意事項
(1)	相同訂單查詢請勿過於頻繁，建議每筆查詢交易間隔30秒，或利用離峰時段執行(如夜間時段)。
(2)	所有參數名稱需區分大小寫，且字元編碼charset固定採用UTF-8。
(3)	若接收端respSignature驗證失敗，請主動利用查詢交易來同步雙方交易結果狀態。
(4)	若逾時(支付寶付款期限為12小時)未收到交易結果，請主動利用查詢交易來同步雙方交易結果狀態。

 
附錄一：回應碼
代碼	說明
0000	成功
0013	境外機構訊息回應逾時異常
0014	尚未執行驗證碼更新交易
0015	驗證碼更新失敗，請確認是否已設定初始驗證碼值
0019	境外機構回傳sign值驗證有誤
0087	不支援此交易類型
0098	境外機構交易處理失敗
0099	不明原因異常
41xx	參數為必帶，其中xx代表錯誤的欄位編號
42xx	格式有錯，其中xx代表錯誤的欄位編號
44xx	非法值，其中xx代表錯誤的欄位編號
C001	讀取匯率異常
C002	REQ交易驗證檢核有誤
H001	僅接受以POST傳送參數
P040	收單行&特店&端末&訂單編號不可重複
R051	已逾退貨期限，無法退貨
R052	退貨編號不可與訂單編號一樣
R053	收單行&特店&端末&退貨編號不可重複
R054	平台代號與原交易平台代號不同
R055	查無原訂單編號
R056	查無原退貨編號
R057	原訂單編號非成功交易
R058	退貨總金額已超過原購貨金額
註：若交易基本檢核有誤將直接以http response方式顯示回應碼，其餘使用POST模式透過「交易結果回傳網址」通知網路特店回應碼。



  
財金公司國際卡收單共用系統
網路特店收單整合技術說明手冊
V 2.5









分發編號	
使用部門	



 財 金 資 訊 股 份 有 限 公 司
  FINANCIAL  INFORMATION  SERVICE  CO. ,LTD






 

版次	生效日期	發行與變更說明
V 1.0	2015/05/20	初版(Draft version)。
V 1.1	2015/12/14	1.	增加網路特店指定顯示公版頁面語系的功能。
2.	調整銀聯交易之特店名稱限定僅能為英、數字。
3.	增加特店人員『參數驗證』設定說明。
4.	增加說明若特店未到授權結果，必須主動發送查詢。
5.	調整3.1.1授權交易輸入欄位-交易金額說明。
6.	調整3.3.3查詢交易輸入欄位-xid為非必要欄位。
7.	調整3.3.4 查詢交易輸出欄位。
V 1.2	2016/02/02	1.	增加交易金額(purchAmt)備註說明，晶片金融卡單筆交易金額最高為200萬。
2.	調整3.1.1授權交易輸入欄位-交易訂單編號說明。
3.	調整3.3.4查詢交易輸出欄位-移除in_purchAmt、out_purchAmt，調整qid字型。
V 1.3	2016/07/13	1.	新增附錄A四『跨境匯入網路電子支付交易作業說明』。
2.	調整銀聯交易之訂單編號長度最大為19。
V 1.4	2016/08/02	1.	調整遠端金融卡交易功能作業說明-3.網路收單特店啟用網路交易驗證作業機制時，要求TOKEN規格。
2.	於3.3輸出入列表章節增加說明：M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位。
V 1.5	2016/12/28	1.	調整3.1.1授權交易輸入欄位-enCodeType、timeoutTime。
2.	調整5.錯誤代碼一覽表，網路收單授權交易回應代碼『Status 12：特店屬性驗證及call MPI回應(ACS之回應會透過MPI傳給FOCAS)』，增加ErrorCode：『01、10、99』。
3.	調整3.1.2授權交易輸出欄位，移除Xid之條件，只需判斷status及authCode。
4.	調整原附錄A四名稱為：『跨境匯入交易作業說明』。
5.	調整附錄A四1：『跨境匯入網路電子支付交易』作業說明。
6.	新增附錄A四2：『跨境匯入O2O交易』等作業說明。
V 1.6	2017/03/20	1.	調整5.錯誤代碼一覽表，5.2 Merchant Plug-In MPI 交易回應代碼，增加Rcode：『2104、2201、2206』。
2.	調整3.2.2訂單查詢輸出欄位增加txstate、lastPan4，調整說明txstatus、txerrcode。
V 1.7	2019/03/29	1.	調整3.1.1新增frontFailUrl銀聯網路收單交易失敗，返回商戶跳轉網址。
2.	調整3.2.2訂單查詢輸出欄位rescode說明。
V 2.0	2019/08/07	1.	增加台灣Pay QR Code主掃付款公版頁面規格說明。
2.	新增附錄A五，銀聯網路收單支援網路交易驗證作業機制。
V 2.1	2020/03/30	1.	調整4.1特店批次請款檔格式之檔案名稱說明。
2.	增加消費扣款訂單查詢網址。
3.	配合銀聯UPOP 5.1調整訂單編號說明、增加查詢輸出欄位說明。
4.	配合客製化頁面調整，調整3.1.1 customize欄位說明及3.4.4 客製化授權網頁編輯規則說明。
5.	補充信用卡訂單查詢回應代碼。
6.	增加銀聯交易回應代碼。
V2.2	2020/11/27	1.	配合臺灣行動支付網路收單業務下線，移除參數規格說明。
2.	配合支援3DS2.0驗證作業，更新5.2 Merchant Plug-In MPI 交易回應代碼。
V2.3	2022/01/11	版號更新。
V2.4	2022/09/23	1.	調整授權回應、訂單查詢回應xid說明。
2.	增加threeDSAuthInd欄位。
3.	更新信用卡訂單查詢匯出xls格式及銀聯卡訂單查詢匯出xls格式。
V2.5	2023/10/31	1.	更新信用卡訂單查詢匯出xls格式及銀聯卡訂單查詢匯出xls格式。
2.	配合3DS2.0驗證作業要求，增加授權交易輸入欄位。
 
目   錄
1.前言	5
2.網路收單共用系統功能介紹	6
2.1網路收單共用系統作業處理流程說明	6
2.2 網路收單共用系統處理概念	7
2.3網路收單共用系統功能說明	8
3.授權交易處理作業說明	10
3.1授權交易處理作業說明	10
3.1.1授權交易輸入欄位	10
3.1.2授權交易輸出欄位	14
3.2 訂單查詢程式介面	18
3.2.1 訂單查詢輸入欄位	18
3.2.2 訂單查詢輸出欄位	19
3.3 輸出入欄位列表	22
3.3.1 授權交易輸入欄位	22
3.3.2 授權交易輸出欄位	23
3.3.3 查詢交易輸入欄位	23
3.3.4 查詢交易輸出欄位	24
3.4 授權交易結果回應介面	24
3.4.1 導址至使用者瀏覽器	24
3.4.2 導址至特店提供網頁	29
3.4.3特店網站設定AuthResURL	30
3.4.4 客製化授權網頁編輯規則	30
3.4.5 訂單查詢整合範例	37
3.4.6 訂單查詢回覆頁範例	38
4.上傳檔案規格	39
4.1 特店批次請款檔格式	39
4.2 信用卡訂單查詢匯出xls格式	41
4.3 銀聯卡訂單查詢匯出xls格式	43
5.錯誤代碼一覽表	45
5.1網路收單授權交易回應代碼	45
5.2 Merchant Plug-In MPI 交易回應代碼	49
5.3信用卡訂單查詢回應代碼	50
5.4銀聯網路收單交易回應代碼	50
6.台灣Pay QR Code主掃付款公版頁面規格訊息	52
6.1交易流程	52
6.2網路商店QR Code付款頁面連線訊息	53
6.3網路商店QR Code付款結果通知訊息	55
6.4網路商店QR Code付款結果查詢訊息	56
附錄 A 網路交易驗證作業機制	57
一、URL網路特店交易驗證作業流程	57
二、TOKEN規格說明	58
三、跨境匯入交易作業說明	60
四、銀聯網路收單交易作業說明	61
五、傳輸欄位加密處理機制	62

1.前言

本手冊主要提供財金公司會員銀行網路特店或特店所委託之資訊承包商，辦理網路交易授權處理流程及說明之用。

2.網路收單共用系統功能介紹
2.1網路收單共用系統作業處理流程說明
首先就網路收單共用系統辦理相關作業處理流程說明如下。
 
 

2.2 網路收單共用系統處理概念
消費者於網路特店完成消費點選付款功能後，特店系統應依本手冊相關說明(以html語法中的hidden方式並傳送相關資料參數(註1))，轉導址至網路收單共用系統進行後續授權作業。
網路收單共用系統接收特店傳送之相關資料參數，並進行相關檢核確認無誤後，顯示付款（刷卡）頁面，由消費者輸入授權交易相關資料，並於完成輸入按確認後，隨即進行授權作業處理，並依授權結果(無論成功或失敗)自動產生回應畫面(至消費者瀏覽器或依特店指示回傳網址，由特店網站產生回應畫面)。特店可以透過瀏覽器，登入網路收單共用系統，執行相關帳務作業或查詢等功能服務。

註1：Pay Page亦會以html語法中的hidden方式傳遞參數，包括uuid、timeout、MerchantID、merID、TerminalID、AutoCap、customize、amtExp、AuthResURL、sslUrl

 
 
2.3網路收單共用系統功能說明
網路收單共用系統主要提供網路收單會員銀行網路特店，辦理消費者購物授權、帳務處理等功能服務，首先先就網路共用系統功能特色簡單說明如下：

(1) Action URL：
使用HTML語法中的POST指令方式，提供以URL模式連線，並提供測試與營運系統不同URL(請詳參本手冊後續相關規格說明)。
(2) 授權介面：
此接收介面，主要的功用在於取得特店網站的相關參數，取得了這些參數，系統在經過檢查正確無誤後，系統就會顯示統一的刷卡頁面供消費者輸入卡號（如特店網站想呈現符合網站的刷卡頁面，本系統亦提供自製頁面的功能；特店僅需申請此功能，即可自製符合商家購物網站風格的刷卡頁面）。
(3) 授權回應：
如特店無設定，則系統有統一畫面可以產生，並產出相關參數可供特店網站進行資料記錄，建議特店網站可以將相關參數記錄至特店網站的系統資料庫之中，以結合會員之購物消費情況；特店亦可設定藉由自行的網站產生授權結果之頁面，如果有授權不成功之部份，特店需自行依本手冊對錯誤碼(Error Code)進行相關處理及顯示。

消費者透過整合付款介面輸入信用卡卡號與有效期限，逕與銀行連線啟動信用卡線上授權處理程序，並將授權結果傳回給特店。
因此，特店網站必須自行設計消費者購物付款結帳的主要網頁，網頁以HTML鏈結網址，即可提供與收單銀行連結的信用卡授權功能。同時，特店網站可選擇性另一個輔助網頁，處理信用卡授權結果的回傳狀態（如果特店未提供此輔助網頁，系統會以預設網頁呈現給消費者，則特店無法收到共用系統回覆之授權結果，必須登入帳務管理系統查詢確認授權結果）。
 

特店網站必須透過HTTPS協定之POST方式傳遞參數資料，茲簡單說明如下：
	測試環境：
•	信用卡網路收單測試連結網址
<form method="POST" action=https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/online/>
•	台灣金融卡Smart Pay網路收單測試連結網址
<form method="POST" action=https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/debit/>
•	銀聯網路收單測試連結網址
<form method="POST" action=https://www.focas-test.fisc.com.tw/FOCAS_UPOP/upop/>

	營運環境：
•	信用卡網路收單營運連結網址
<form method="POST" action=https://www.focas.fisc.com.tw/FOCAS_WEBPOS/online/>
•	台灣金融卡Smart Pay網路收單營運連結網址
<form method="POST" action=https://www.focas.fisc.com.tw/FOCAS_WEBPOS/debit/>
•	銀聯網路收單營運連結網址
<form method="POST" action=https://www.focas.fisc.com.tw/FOCAS_UPOP/upop/>

3.授權交易處理作業說明
3.1授權交易處理作業說明
3.1.1授權交易輸入欄位
共用系統整合介面相關輸入參數欄位名稱為英文字(大、小寫有別)，字元編碼charset固定採用Big-5，相關說明如下：

merID	(型態:N,最大長度10位)
	網站特店自訂代碼(請注意merID與MerchantID不同)。

MerchantID	(型態:AN,固定長度15位)
	收單銀行授權使用的特店代號(由收單銀行編製提供)。

TerminalID	(型態:AN,固定長度8位)
	收單銀行授權使用的機台代號(由收單銀行編製提供)。

MerchantName	(型態:ANS,最大長度60位)
	特店網站或公司名稱，僅供顯示。
	銀聯交易限定僅能為英、數字、空白及『-』，最大長度25位。

customize	(型態:AN,固定長度1位)
	客製化付款授權網頁辨識碼。
0表不使用客製化授權頁。
1表使用第一個版本、2表使用第二個版本，依此類推，目前最大版本號為8。

lidm	(型態:AN,長度說明如下)
	交易訂單編號，建議訂單編號不可重複編號。
信用卡交易，最大長度19位。
銀聯卡交易，最小長度8位，最大長度19位(不含ExpressPay交易)，不可使用符號「-」及「_」。
消費扣款交易，最大長度16位。

purchAmt	(型態:N,最大長度10位)
	交易金額 (臺幣金額整數)。
(註：晶片金融卡單筆交易金額最高為200萬。)

CurrencyNote		(型態:AN,最大長度50位)
	註記說明，僅供顯示。

AutoCap		(型態:AN,固定長度1位)
	是否自動轉入請款檔作業。
0 表示不轉入請款檔(預設)。
1 表示自動轉入請款檔。

AuthResURL	(型態:ANS,最大長度512位)
	授權結果回傳網址。

frontFailUrl	(型態:ANS,最大長度256位)
	銀聯網路UPOP交易失敗，返回商戶跳轉網址。

PayType	(型態:AN,固定長度1位)
	交易類別碼。
0 表示本筆交易為一般交易(預設)。
1 表示本筆交易為分期交易。
2 表示本筆交易為紅利交易。

PeriodNum	(型態:N,最大長度2位)
	分期交易之期數。

BonusActionCode	(型態:AN,固定長度6位)
	紅利交易活動代碼。

LocalDate	(型態:N,固定長度8位)
	購買地交易日期(yyyymmdd)(預設為系統日期)。

LocalTime	(型態:N,固定長度6位)
	購買地交易時間(HHMMSS) (預設為系統時間)。

reqToken	(型態:AN,最大長度64位)
	交易驗證碼。

subMerchID	(型態:AN,固定長度8位)
	次特店代號。

enCodeType	(型態:ANS,最大長度10位)
	網頁編碼格式(預設為BIG5)，若使用UTF-8進行編碼，請再傳送的頁面中增加一輸入欄位enCodeType，值設定為UTF-8。

timeoutDate	(型態:N,固定長度8位)
	設定交易逾時日期(yyyymmdd)。

timeoutTime	(型態:N,固定長度6位)
	設定交易逾時起始時間(HHMMSS)。

timeoutSecs	(型態:N,最大長度3位)
	交易逾時秒數，最大值為600秒。

walletVerifyCode	(型態:N,最大長度4~6位)
	網路交易驗證碼。

isSynchronism	(型態:N,固定長度1位)
	同步/非同步標記。
0 表示本筆交易為同步交易。
1 表示本筆交易為非同步交易。

lagSelect	(型態:N,固定長度1位)
	預設顯示語系。
0或其他 表示中文(繁)。
1 表示中文(简)。
2 表示English。
3 表示日本語。

threeDSAuthInd	(型態:N,固定長度4位)
	3D交易驗證類型，若該特店設定支援3D交易，前端未帶此欄位，則預設為0101。
0101 表示支付類交易驗證(PA)的Payment transaction。
0204 表示非支付類交易驗證(NPA)的Add card。
0205 表示非支付類交易驗證(NPA)的Maintain card。
註：綁卡驗證應使用0204或0205。

billAddrCity	(型態:ANS,最大長度50位)
	持卡人信用卡帳單英文地址的城市，僅供3D交易驗證使用。

billAddrCountry	(型態:N,固定長度3位)
	持卡人信用卡帳單地址的國別碼，依循ISO 3166-1標準的三位數字代碼，僅供3D交易驗證使用，若billAddrState欄位有值，則此欄位必帶。

billAddrLine1	(型態:ANS,最大長度50位)
	持卡人信用卡帳單英文地址，僅供3D交易驗證使用，本欄位須經加密處理，詳見附錄A五說明。

billAddrPostCode	(型態:ANS,最大長度16位)
	持卡人信用卡帳單地址的郵遞區號，僅供3D交易驗證使用。

billAddrState	(型態:ANS,最大長度3位)
	持卡人信用卡帳單英文地址的State/Province，僅供3D交易驗證使用。

cardholderEmail	(型態:ANS,最大長度254位)
	持卡人Email位址，依循IETF RFC 5322的Section 3.4標準格式，僅供3D交易驗證使用，本欄位須經加密處理，詳見附錄A五說明。

cardholderMobilePhone	(型態: NS,最大長度19位)
	持卡人手機號碼，依循ITU-E.164標準格式，資料組成方式為『國家代碼(1到3位數字)-用戶號碼(最多15位數字)』，例如『886-990123456』，僅供3D交易驗證使用，本欄位須經加密處理，詳見附錄A五說明。

cardholderName	(型態: ANS,最大長度45位)
	持卡人英文姓名，僅供3D交易驗證使用，本欄位須經加密處理，詳見附錄A五說明。

IV	(型態: H,固定長度24位)
	加密使用之IV值，長度固定12 bytes，即為Hex String24字元，詳見附錄A五說明，若有傳遞須加密之欄位，則此欄位必帶。

 

3.1.2授權交易輸出欄位
共用系統於接收特店傳送交易，經持卡人輸入相關驗證後，進行交易授權處理，並依處理結果傳送本節相關輸出參數資料，特店應依共用系統輸出參數應判斷後續處理方式(請注意輸出參數字元編碼charset固定採用Big-5，若信用卡交易status=0且authCode非空值時，才能確認該筆交易成功授權)。
另，特店有設定『主動發送授權通知網址』，則信用卡授權結果主動通知訊息之輸入欄位與授權交易輸出欄位一致，主動發送授權通知在商店端未正常回應http 200的情況下會重試最多3次。
通知訊息之內容範例如下：
AuthResp={errcode=30, authCode=null, authRespTime=20200219173137, lastPan4=9104, amtExp=0, xid=O-OBJECT-20200219173058.811-0025, errDesc=授權失敗, lidm=FOCAS-T200219173057, authAmt=200, merID=829, currency=901, cardBrand=VISA, pan=480254******9104, status=8}
為一字串，開頭固定為AuthResp=，大括號{}內的各欄位以半形逗號區隔。
(如與輸入欄位相同，則不列以下欗位說明。)

status	(型態:N,固定長度1位)
	授權結果狀態。

errcode	(型態:N,固定長度2位)
	錯誤代碼。

authCode	(型態:N,固定長度6位)
	交易授權碼。

authAmt	(型態:N,最大長度10位)
	授權金額 (臺幣金額整數)。

lidm	(型態:AN,長度說明如下)
	交易訂單編號。
信用卡交易，最大長度19位。
銀聯卡交易，最小長度8位，最大長度19位。
消費扣款交易，最大長度16位。

xid	(型態:ANS,最大長度40位)
	交易追蹤碼(此處回傳xid非3D網路交易序號)。

currency	(型態:AN,固定長度3位)
		幣別(901)。

amtExp	(型態:N,最大長度10位)
		幣值指數(小數點三位)。

merID	(型態:N,最大長度10位)
	網站特店自訂代碼(請注意merID與MerchantID不同)。

MerchantName	(型態:ANS,最大長度60位)
	特店網站或公司名稱，僅供顯示。
	銀聯交易限定僅能為英、數字、空白及『-』，最大長度25位。

errDesc	(型態:AN,最大長度512位)
		授權失敗原因說明。

lastPan4	(型態:AN,固定長度4位)
		持卡者交易的信用卡號末四碼。

cardBrand	(型態:AN,長度3~10位)
		持卡者交易的信用卡別，如VISA、MasterCard、JCB。

pan	(型態:AN,最大長度19位)
		遮罩後之信用卡卡號。

authRespTime	(型態:N,最大長度19位)
	授權處理回應時間，格式為YYYYMMDDHHMMSS。
	銀聯交易處理回應時間格式為YYYY/MM/DD HH:MM:SS。

PayType	(型態:AN,固定長度1位)
	交易類別碼。

PeriodNum	(型態:N,最大長度2位)
	分期交易之期數。

DownPayments	(型態:N,最大長度10位)
	分期交易之首期分期金額，含2位小數。

InstallmentPayments(型態:N,最大長度10位)
	分期交易之每期金額，含2位小數。

BonusActionCode	(型態:AN,固定長度6位)
	紅利交易活動代碼。

BonusDesc	(型態:AN,固定長度1位)
	紅利折抵方式。
0 表示依發卡行決定(預設)。
1 表示不折抵。
2 表示全數折抵。
3 表示部份折抵。

BonusRespCode	(型態:AN,固定長度2位)
	紅利交易授權結果(00為成功)。

BonusSign	(型態:AN,固定長度1位)
	紅利剩餘點數註記。
P 表示紅利餘額點數為正數。
N 表示紅利餘額點數為負數。

BonusBalance	(型態:AN,固定長度10位)
	紅利餘額點數。

BonusDeduct	(型態:AN,固定長度10位)
	扣抵紅利點數。

BonusDebuctAmt	(型態:AN,固定長度12位)
	紅利扣抵後交易金額，含2位小數。

respToken	(型態:AN,最大長度64位)
	交易驗證碼。

txnDateLocal	(型態:AN,最大長度8位)
	交易日期。

txnTimeLocal		(型態:AN,最大長度6位)
	交易時間。

Srrn		(型態:N,最大長度12位)
	系統追蹤號碼。

respCode	(型態:N,最大長度4位)
	回應碼。
	銀聯交易的此欄位值為00表示成功。

respMsg	(型態:N,最大長度512位)
	回應結果描述。

qid	(型態:N,最大長度512位)
	由銀聯回覆之交易識別值。
 
3.2 訂單查詢程式介面
訂單查詢程式是提供特店網站系統直接查詢訂單狀態的整合介面，主要讓特店網站經由此面再次查詢訂單的授權結果，而可省略登入FOCAS帳務系統的動作。此整合介面無須人工輸入資料的作業，故建議以背景程式整合到特店的網站系統，且Internet網路訊息無法保證送達，故特店應針對沒有收到授權結果通知的訂單，主動透過此查詢介面確認該訂單的狀態。
3.2.1 訂單查詢輸入欄位
輸入欄位如下說明，請注意，各定義欄位號名稱大、小寫有別。
merID	(型態:N,最大長度10位)
	網站特店自訂代碼(請注意merID與MerchantID不同)。

MerchantID	(型態:AN,固定長度15位)
	收單銀行授權使用的特店代號(由收單銀行編製提供)。

TerminalID	(型態:AN,固定長度8位)
	收單銀行授權使用的機台代號(由收單銀行編製提供)。

lidm	(型態:AN,長度說明如下)
	交易訂單編號。
信用卡交易，最大長度19位。
銀聯卡交易，最小長度8位，最大長度19位。
消費扣款交易，最大長度16位。

purchAmt	(型態:N,最大長度10位)
	交易金額 (臺幣金額整數)。
(註：晶片金融卡單筆交易金額最高為200萬。)

ResURL	(型態:ANS,最大長度512位)
	授權結果回傳網址。

xid	(型態:ANS,最大長度40位)
	交易追蹤碼(此處xid非3D網路交易序號)。
 

3.2.2 訂單查詢輸出欄位
特店可利用ResURL這個參數在指定查詢結果由特店之網頁程式產出，以下輸出欄位會以POST Method方式回傳給特店網站所提供之 ResURL，如特店網站未提供此URL，則不會回傳以下參數給特店網站，特店將無從得知查詢結果。
(如與輸入欄位相同，則不列以下欗位說明。)

merID	(型態:N,最大長度10位)
	網站特店自訂代碼(請注意merID與MerchantID不同)。

MerchantID	(型態:AN,固定長度15位)
	收單銀行授權使用的特店代號(由收單銀行編製提供)。

TerminalID	(型態:AN,固定長度8位)
	收單銀行授權使用的機台代號(由收單銀行編製提供)。

txstate	(型態:AN,最大長度4位)
	授權結果狀態。
0表示僅授權成功。
1表示已經轉入請款檔。
2表已轉出請款檔。
3表取消訂單。
4表已退貨成功。
5表已取消退貨。
8表已退貨結帳。
9表已請款結帳。
X表授權失敗訂單。
txstatus
	錯誤代碼。
請對照參考5.1網路收單授權交易回應代碼之Status欄位。
txerrcode	(型態:AN,固定長度2位)
	錯誤代碼。
請搭配txstatus欄位，對照參考5.1網路收單授權交易回應代碼之ErrorCode欄位。
lidm	(型態:AN,長度說明如下)
	交易訂單編號。
信用卡交易，最大長度19位。
銀聯卡交易，最小長度8位，最大長度19位。
消費扣款交易，最大長度16位。

purchAmt	(型態:N,最大長度10位)
	交易金額 (臺幣金額整數)。
(註：晶片金融卡單筆交易金額最高為200萬。)

xid	(型態:ANS,最大長度40位)
	交易追蹤碼(此處回傳xid非3D網路交易序號)。

authCode	(型態:N,最大長度6位)
	交易授權碼。

rescode	(型態:AN,最大長度2位)
	訊息回應代碼。
0：查詢成功。
1：merID或MerchantID或TerminalID輸入不完整。
2：未指定訂單編號。
3：未指定訂單金額。
10：查不到符合條件之訂單資訊。
11：訂單資料超過一筆。
PayType	(型態:AN,固定長度1位)
	交易類別碼。

PeriodNum	(型態:N,最大長度2位)
	分期交易之期數。

DownPayments	(型態:N,最大長度8位)
	分期交易之首期分期金額(臺幣金額整數)。

InstallmentPayments	(型態:N,最大長度8位)
	分期交易之每期金額(臺幣金額整數)。

lastPan4	 (型態:N,最大長度4位)
	卡號後4碼。

multiRecords	 (型態:ANS,長度說明如下)
	當符合本次信用卡交易查詢條件之訂單資料超過1筆時才會收到此參數。資料內容範例如下：totalRecords=2;record0:txstatus=0,txstate=0,txerrcode=00,authCode=090545,purchAmt=1000,xid=O-OBJECT-20121019132203.666-0001,localDate=20121019;localTime=132210;PayType=1;record1:txstatus=0,txstate=2,txerrcode=00,authCode=546875,purchAmt=1000,xid=O-OBJECT-20121019154436.111-0002,localDate=20121019;localTime=154448;PayType=0;
	當符合本次消費扣款查詢購物交易條件之訂單資料超過1筆時才會收到此參數。資料內容範例如下：
totalRecords=2;record0:txstatus=0,txstate=9,purchAmt=000000015000,xid=O-FAPI-20200113164148.351-0126,localDate=20200113;localTime=164138;record1:txstatus=0,txstate=X,purchAmt=000000015000,xid=O-FAPI-20200113164116.507-0125,localDate=20200113;localTime=164109;

transType	(型態:N,固定長度2位)
	銀聯交易類型 ：31：取消交易、01：購貨交易、04：退貨交易。
 
3.3 輸出入欄位列表
(本節各欄位說明，詳見『3.1授權交易處理作業說明』)
3.3.1 授權交易輸入欄位
輸入欄位	信用卡授權交易	消費扣款	UPOP
	一般/紅利	分期交易		
merID	M	M		
MerchantID	M	M	M	M
TerminalID	M	M	M	M
MerchantName	O	O	M	M
customize	M	M		
lidm	M	M	M	M
purchAmt	M	M	M	M
CurrencyNote	O	O		O
AutoCap	O	O		
AuthResURL	O	O	O	O
frontFailUrl				O
PayType	O	M		
PeriodNum		M		
BonusActionCode	C			
LocalDate	O	O		
LocalTime	O	O		
reqToken	C	C		C
subMerchID	C	C		
enCodeType	O	O		
timeoutDate	C	C		
timeoutTime	C	C		
timeoutSecs	C	C		
Currency				O
lagSelect	O	O		
threeDSAuthInd	C	C		
billAddrCity	O	O		
billAddrCountry	C	C		
billAddrLine1	O	O		
billAddrPostCode	O	O		
billAddrState	O	O		
cardholderEmail	O	O		
cardholderMobilePhone	O	O		
cardholderName	O	O		
IV	C	C		
M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位


3.3.2 授權交易輸出欄位
輸出欄位	信用卡授權交易	消費扣款	UPOP
	一般/紅利	分期交易		
status	M	M	M	
errcode	M	M	M	
authCode	M	M		
MerchantID			M	M
TerminalID			M	M
authAmt	M	M	M	M
lidm	M	M	M	M
xid	M	M	M	
currency	M	M		
amtExp	M	M		
merID	M	M		
errDesc	M	M	M	
lastPan4	M	M		
cardBrand	M	M		M
pan	M	M		M
authRespTime	M	M		M
PayType		M		
PeriodNum		M		
DownPayments		M		
InstallmentPayments		M		
BonusActionCode	M			
BonusDesc	M			
BonusRespCode	M			
BonusSign	M			
BonusBalance	M			
BonusDeduct	M			
BonusDebuctAmt	M			
respToken	C	C		C
BankName			M	
MerchantName			M	M
respMsg				M
respCode				M
qid				M
M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位


3.3.3 查詢交易輸入欄位
輸入欄位	信用卡查詢交易	消費扣款	UPOP
	一般/紅利	分期交易		
merID	M	M		
MerchantID	M	M	M	M
TerminalID	M	M	M	M
lidm	M	M	M	M
purchAmt	M	M	M	M
ResURL	M	M	M	O
xid	O	O		
M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位


3.3.4 查詢交易輸出欄位
輸出欄位	信用卡查詢交易	消費扣款	UPOP
	一般/紅利	分期交易		
merID	M	M		
MerchantID	M	M	M	M
TerminalID	M	M	M	M
txstate	M	M	M	M
txerrcode	M	M		M
lidm	M	M	M	M
purchAmt	M	M	M	M
xid	M	M	M	
authCode	M	M		
rescode	M	M	M	M
payType		C		
PeriodNum		C		
DownPayments		C		
InstallmentPayments		C		
multiRecords	M	M		
transType				M
qid				M
amtRefundTotal	C	C		
txstatus	M		M	
M：必帶欄位；C：特定條件下必帶欄位；O：選帶欄位



3.4 授權交易結果回應介面 
3.4.1 導址至使用者瀏覽器
以下是特店給消費者結帳簡易網頁範例，其中第五列的action參數，
測試環境請使用https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/online/
營運環境請使用https://www.focas.fisc.com.tw/FOCAS_WEBPOS/online/
<html><head><title>Sample</title></head>
<body bgcolor="#FFFFFF">
<center>
<!-- PurchAmt, currency, amtExp, lidm, merID ,AutoCap 由特店填入value -->
<form method=post action=https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/online/>
<input type=hidden name=MerchantID value=950123456789101>
<input type=hidden name=TerminalID value=91010001>
<input type=hidden name=merID value=12345678>
<input type=hidden name=MerchantName value=PowerTV>
<input type=hidden name=purchAmt value=350>
<input type=hidden name=lidm value=000000000003a8ad1fa>
<input type=hidden name=AutoCap value=1>
<table width=540  bgcolor=#FFE680>
<tr bgcolor=#FFA87D align=center><td colspan=3>訂購物品範例</td></tr>
<tr bgcolor=#FFE880>
<td>訂單編號：<font color=#FF50000>000000000003a8ad1fa</font></td>
</tr><tr bgcolor=#FFE880>
<td width=40%%><b>品名</b></td>
<td width=30%%><b>數量</b></td>
<td width=30%%><b>價格</b></td>
</tr><tr> 
<td width=40%%>HyShop開店軟體</td>
<td width=30%%>1</td>
<td widht=30%%><b><font color=#FF5000>NT. 186</font></b></td>
</tr>
</table>
<input type=submit name=submit value="結帳">
</form></center></body> </html>

其他卡別之action參數

系統別	台灣金融卡Smart Pay網路收單整合介面網址
測試套	https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/debit/
營運套	https://www.focas.fisc.com.tw/FOCAS_WEBPOS/debit/

系統別	銀聯網路收單訂單查詢網址
測試套	https://www.focas-test.fisc.com.tw/FOCAS_UPOP/upoporderInquery/
營運套	https://www.focas.fisc.com.tw/FOCAS_UPOP/upoporderInquery/

系統別 消費扣款訂單查詢網址
測試套 https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/debitOrderInquery/
營運套 https://www.focas.fisc.com.tw/FOCAS_WEBPOS/debitOrderInquery/

特店於自行架構網站之購物車之結帳畫面（以下畫面為模擬畫面）
 
 

消費者點選結帳，系統會重導（Re-direct Link）至本系統，系統將出現刷卡頁面。

 
 

此範例中，特店網路在AuthResURL 沒有設定，故系統會以內定網頁顯示授權結果

 
 

3.4.2 導址至特店提供網頁
當特店網站設定AuthResURL 時，必須自行提供顯示網頁，如下之AuthResURL 參數即設定 http://www.xxx.com.tw/xyz.asp ，此處的處理方式請參考『3.4.3特店網站設定AuthResURL』範例，這裡是以ASP為例，可依特店網站之技術人員
<html><head><title>Sample</title></head>
<body bgcolor="#FFFFFF">
<center>
<!-- PurchAmt, currency, amtExp, lidm, merID ,AutoCap 由特店填入value -->
<form method=post action=https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/online/>
<input type=hidden name=MerchantID value=950123456789101>
<input type=hidden name=TerminalID value=91010001>
<input type=hidden name=merID value=12345678>
<input type=hidden name=MerchantName value=PowerTV>
<input type=hidden name=purchAmt value=350>
<input type=hidden name=lidm value=000000000003a8ad1fa>
<input type=hidden name=AutoCap value=1>
<input type=hidden name=AuthResURL value= https://www.xxx.com.tw/xyz.asp>
<table width=540  bgcolor=#FFE680>
<tr bgcolor=#FFA87D align=center><td colspan=3>訂購物品範例</td></tr>
<tr bgcolor=#FFE880>
<td>訂單編號：<font color=#FF50000>000000000003a8ad1fa</font></td>
</tr>
<tr bgcolor=#FFE880>
<td width=40%%><b>品名</b></td>
<td width=30%%><b>數量</b></td>
<td width=30%%><b>價格</b></td>
</tr>
<tr> 
<td width=40%%>HyShop開店軟體</td>
<td width=30%%>1</td>
<td widht=30%%><b><font color=#FF5000>NT. 186</font></b></td>
</tr>
</table>
<input type=submit name=submit value="結帳">
</form></center></body> </html>
3.4.3特店網站設定AuthResURL
特店網站提供AuthResURL授權結果通知網頁範例（以ASP為例）
(註：若未收到授權結果通知，請主動發送查詢，詳見『3.4.5 訂單查詢整合範例』)
以下就是例子為<input type=hidden name=AuthResURL value= http://www.xxx.com.tw/xyz.asp>中的 xyz.asp程式
<html><head><title>AuthResHandler</title></head>
<body>
<%
status = Request("status")
merID=Request("merID")
if (status=0) then
   authCode = Request("authCode")
   lidm = Request("lidm")
   xid = Request("xid")
   authAmt = Request("authAmt")
  ‘省略顯示授權成果畫面及相關訂單資料庫動作
else
   errcode = Request("errcode")
   errDesc = Request("errDesc")
   ‘省略顯示授權失敗畫面及相關訂單資料庫動作
end if
%>
</body> 
</html>
上述程式省略了一些關於美工圖案的顯示，及記入資料庫的動作，建議特店網站務必要把傳回來的值存入特店網站的資料庫之中，以便進行系統之交易記錄查核。下圖示簡易的ASP參數接收的資料範例：
 
3.4.4 客製化授權網頁編輯規則
網路收單共用系統提供特店自製信用卡付款網頁功能，由特店傳送客製網頁檔(.htm或.html)其內容相關規範請參考下列範例，以便FOCAS WEB Server能辨識資料及傳送參數，共用系統對於特店上傳的客製化授權頁檔案不進行任何置換動作，如有需要使用圖片，應存放在特店可提供存取的主機，同時應於本公司測試環境進行完成測試作業，再進行營運系統轉換，以確保客製化網頁正確無誤。
若為Chrome版本V74以上 或Safari的瀏覽器，請改用版本5-8，避免顯示異常。
系統別	客製化授權網頁網址
測試套	https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/customizeOnline/
營運套	https://www.focas.fisc.com.tw/FOCAS_WEBPOS/customizeOnline/
參數套用範例：<input name="後端參數" type="hidden" value="前端參數"/>
欄位名稱	前端參數	後端參數	欄位使用說明
特店代號	<$MerchantID$>	MerchantID	必要欄位。
端末代號	<$TerminalID$>	TerminalID	必要欄位。
訂單編號	<$lidm$>	lidm	必要欄位。
消費金額	<$purchAmt$>	purchAmt	必要欄位。
交易追蹤碼	<$UUID$>	UUID	必要欄位，無須給值，相關值將由系統自行產生以利追蹤交易。
卡號	N/A	pan_no1~ pan_no4	必要欄位。
卡片背面未三碼	N/A	cvc2	必要欄位。
效期—年	N/A	expire_year	必要欄位。
效期—月	N/A	expire_month	必要欄位。
交易型態	<$PayType$>	PayType	必要欄位。
0：一般交易(預設)。
1：分期交易。
2：紅利交易。
紅利交易活動代碼	<$BonusActionCode$>	BonusActionCode	紅利交易必要欄位。
分期期數	<$PeriodNum$>	PeriodNum	分期交易必要欄位。
特店名稱	<$MerchantName$>	MerchantName	非必要欄位。
授權結果通知網頁	<$AuthResURL$>	AuthResURL	非必要欄位，未帶由財金公版頁面呈現。
自動轉入請款檔	<$AutoCap$>	AutoCap	非必要欄位，
0：非自動轉入(預設)，
1：自動轉入。
特店網站自代碼	<$merID$>	merID	非必要欄位。
交易金額註記說明	<$CurrencyNote$>	CurrencyNote	非必要欄位。
3D交易驗證類型	<$threeDSAuthInd$>	threeDSAuthInd	非必要欄位。
帳單英文城市	<$billAddrCity$>	billAddrCity	非必要欄位。
帳單國別	<$billAddrCountry$>	billAddrCountry	若billAddrState欄位有值，則此欄位必帶。
帳單英文地址	<$billAddrLine1$>	billAddrLine1	非必要欄位。
帳單郵遞區號	<$billAddrPostCode$>	billAddrPostCode	非必要欄位。
帳單英文州/省	<$billAddrState$>	billAddrState	非必要欄位。
持卡人Email	<$cardholderEmail$>	cardholderEmail	非必要欄位。
持卡人手機號碼	<$cardholderMobilePhone$>	cardholderMobilePhone	非必要欄位。
持卡人姓名	<$cardholderName$>	cardholderName	非必要欄位。
加密使用IV	<$IV$>	IV	若有傳遞加密欄位，則此欄位必帶。
(參數名稱英文字體大小寫必須與上述範例一致)
 
AuthPage範例畫面
 
範例程式：
<html>
<head>
	<title>財金客製化頁面範例</title>
</head>
<body>
<center>
<form name="fm" method="post" action="https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/customizeOnline/" >
<Table id="credit_card" border="1" cellpadding="1" cellspacing="1" aligin="Center" style="width:710px">
	<tr>
		<td colspan=2 style="text-align:center">客製化刷卡付款範例</td>
	</tr>
	<tr>
		<td>特店名稱：</td>
		<td><span id="lidmSpan" name="MerchantName"><$MerchantName$></span></td>
	</tr>
	<tr>
		<td>特店代碼：</td>
		<td><span id="lidmSpan" name="MerchantID"><$MerchantID$></span></td>
	</tr>
	<tr>
		<td>端末代碼：</td>
		<td><span id="lidmSpan" name="TerminalID"><$TerminalID$></span></td>
	</tr>
	<tr>
		<td>紅利代碼：</td>
		<td><span id="lidmSpan" name="BonusActionCode"><$BonusActionCode$></span></td>
	</tr>
	<tr>
		<td>訂單編號：</td>
		<td><span id="lidmSpan" name="lidm"><$lidm$></span></td>
	</tr>
	<tr>
		<td>信用卡卡號：</td>
		<td>
			<input type=text name=pan_no1 size=4 value="" maxlength=4">
			-<input type=text name=pan_no2 size=4 value="" maxlength=4">
			-<input type=text name=pan_no3 size=4 value="" maxlength=4">
			-<input type=text name=pan_no4 size=4 value="" maxlength=4">
		</td>
	</tr>
	<tr>
		<td>信用卡消費金額：</td>
		<td><span id="purchAmtSpan" name="purchAmt"><$purchAmt$></span></td>
	</tr>
		<td>信用卡到期日：</td>
		<td>
				<select name=expire_month>
						<option value=01>01</option>
						<option value=02>02</option>
						<option value=03>03</option>
						<option value=04>04</option>
						<option selected value=05>05</option>
						<option value=06>06</option>
						<option value=07>07</option>
						<option value=08>08</option>
						<option value=09>09</option>
						<option value=10>10</option>
						<option value=11>11</option>
						<option value=12>12</option>
				</select>
				-
				<select name=expire_year>
						<option value=2013>13</option>
						<option value=2014>14</option>
						<option value=2015>15</option>
						<option value=2016>16</option>
						<option value=2017>17</option>
						<option value=2018>18</option>
						<option value=2019>19</option>
						<option value=2020>20</option>
						<option value=2021>21</option>
						<option value=2022>22</option>
						<option value=2023>23</option>
						<option value=2024>24</option>
						<option value=2025>25</option>
				</select>
		</td>
	</tr>
	<tr>
		<td>信用卡檢查碼：</td>
		<td><input type=text name=cvc2 size=3 maxlength=3 autocomplete=off onKeyUp="setBlur(this);"></td>
	</tr>
	<tr>
		<td colspan=2>
				<center><input type=submit name="send_bt" value="確定送出"></center>
		</td>
	</tr>

<input name="MerchantID" type="hidden" value="<$MerchantID$>"/>
<input name="TerminalID" type="hidden" value="<$TerminalID$>"/>
<input name="lidm" id="lidm" type="hidden" value="<$lidm$>"/>
<input name="purchAmt" id="purchAmt" type="hidden" value="<$purchAmt$>"/>
<input name="UUID" id="UUID" type="hidden" value="<$UUID$>"/>
<input name="PayType" type="hidden" value="<$PayType$>"/>

<input name="BonusActionCode" type="hidden" value="<$BonusActionCode$>"/>
<input name="PeriodNum" type="hidden" value="<$PeriodNum$>"/>
<input name="AuthResURL" type="hidden" value="<$AuthResURL$>"/>
<input name="AutoCap" type="hidden" value="<$AutoCap$>"/>
<input name="merID" type="hidden" value="<$merID$>"/>

</table>
</form>
</center>
</html>

 
3.4.5 訂單查詢整合範例
以下是特店查詢訂單狀態的簡易網頁範例，其中第五列的action參數，
測試階段請使用https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/orderInquery/，正式營運調整該參數為https://www.focas.fisc.com.tw/FOCAS_WEBPOS/orderInquery/即可。

<html><head><title>Sample</title></head>
<body bgcolor="#FFFFFF">
<center>
<!-- PurchAmt, currency, amtExp, lidm, merID ,AutoCap 由特店填入value -->
<form name=fm method=post action= https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/orderInquery/>
<input type=hidden name=MerchantID value=950205079801475>
<input type=hidden name=TerminalID value=91010001>
<input type=hidden name=merID value=12345678>
<input type=hidden name=purchAmt value=350>
<input type=hidden name=lidm value=000000000003a8ad1fa>
<input type=hidden name=ResURL value=https://samplesite.com.tw/Inquiry.asp?para1=自行定義參數&para2=自行定義參數>
</form>
<script Language=JavaScript>
<!--
    this.fm.submit();
//-->
</script>
</center></body></html>

 

3.4.6 訂單查詢回覆頁範例
如3.4.5之程式 ResURL參數指定的Inquiry.asp，其簡易範例如下：

<html>
<body>
merID=<%=Request("merID")%><BR>
lidm=<%=Request("lidm")%><BR>
xid=<%=Request("xid")%><BR>
purchAmt=<%=Request("purchAmt")%><BR>
status=<%=Request("txstatus")%><BR>
errcode=<%=Request("txerrcode")%><BR>
authCode=<%=Request("authCode")%><BR>
state=<%=Request("txstate")%><BR>
rescode=<%=Request("rescode")%><BR>
para1=<%=Request("para1")%><BR>
para2=<%=Request("para2")%><BR>
</body>
</html>


4.上傳檔案規格
供特店網站批次請款檔上傳使用
4.1 特店批次請款檔格式
檔案名稱：	AAAAAAAAAAYYMMDD.SS（西元年）		
AAAAAAAAAA: 網路特店專用流水號
YY: 西元年
MM: 月
DD : 日
SS:  Serial No.
如 特店編號128者，請款檔名為 128030116.01, 128030116.02等

固定長度： 250 BYTE
檔案內每列以 ‘0a’ 作為區隔符號
文數字(A, AN)，左靠，右補空白
數字(N)，右靠，左補零
分期交易不可以進行部分請款，必須是「全額」請款。

Header Record
欄位序號	位置	欄位名稱	型式	長度	說明	客戶輸入	系統輸出
01	01	記錄型別	A	3	Header Record固定為”FHD”	V	V
02	04	請款日期	N	6	送件日期yymmdd西元年	V	V
03	10	批號	N	2	"01"，即檔名的SS值	V	V
04	12	一般購貨請款筆數	N	8		V	V
05	20	一般購貨請款金額	N	15	含二位小數位，如12300表示123.00元	V	V
06	35	保留欄位	AN	216	空白		

 

Detail Record
欄位序號	位置	欄位名稱	型式	長度	說明	客戶輸入	系統輸出
01	01	記錄型別	A	3	Detail Record固定為”FDT”	V	V
02	04	代理行	N	3	收單行庫代號，MerchantID前三碼	V	V
03	07	保留欄位	AN	12	空白		
04	19	訂單編號	AN	19	訂單編號	V	V
05	38	保留欄位	AN	2	空白		
06	40	保留欄位	AN	2	空白		
07	42	交易狀態	AN	12	值域：
cap：請款成功
capfail：請款失敗
authexpire：超過期限
amtexcess：金額過大－大於授權金額
settle：請款結帳		V
08	54	交易金額	N	13	含二位小數位(應小於或等於授權金額)	V	V
09	67	保留欄位	AN	51	空白		
10	118	端末代號	N	8	Terminal ID	V	V
11	126	特店代號	AN	15	Merchant ID	V	V
12	141	授權碼	AN	6		V	V
13	147	執行結果碼(status)	N	2	固定為00(參考第三章)	V	V
14	149	錯誤回覆碼(errcode)	AN	2	固定為00(參考第三章)	V	V
15	151	保留欄位	AN	100	空白		


 

4.2 信用卡訂單查詢匯出xls格式
欄位序號	欄位名稱	型式	長度	說明
01	訂單編號	AN	19	訂單編號
02	轉入批次號碼	N	19	轉入批次號碼
03	退貨批次號碼	N	19	退貨批次號碼
04	最後退貨日期	N	8	YYYYMMDD(西元年+月份+日期)，ex：20120620，若無退貨，則預設為8個0，ex：00000000
05	最後退貨時間	N	9	hhmmssSSS(時+分+秒+毫秒)，ex：213525314，若無退貨，則為空白
06	轉入日期	N	8	YYYYMMDD(西元年+月份+日期)，ex：20120620，若尚未轉入或是已轉出的狀態，則為8個0，ex：00000000
07	轉入時間	N	9	hhmmssSSS(時+分+秒+毫秒)，ex：213525314，若尚未轉入或是已轉出的狀態，則為空白
08	異動日期	N	8	最後異動日期，YYYYMMDD(西元年+月份+日期)，ex：20120620
09	異動時間	N	9	最後異動時間，hhmmssSSS(時+分+秒+毫秒) ，ex：213525314
10	授權狀態	N	1	0表授權成功，其它各類狀態請參考「錯誤代碼一覽表」詳細說明
11	卡號	AN	16	信用卡卡號，僅顯示前6後4遮罩過的卡號，ex：499937******3104，中間6碼顯示為*號
12	交易金額	N	12	共12碼，最後兩碼為小數兩位固定值00，其餘右靠左補0，ex：000002850000，代表交易金額為28,500元
13	轉入金額(新台幣)	N	12	若已轉入，則此欄位會有值，左側未補0，右側仍保留小數兩位固定值00，ex：79100(實際金額791)；若已轉出，或尚未轉入，則為空值
14	最近一次退貨金額	N	12	若已退貨，則此欄位會有值，左側未補0，右側仍保留小數兩位固定值00，ex：79100(實際金額791)；若已取消退貨，或尚未退貨，則為空值
15	交易序號	N	6	Trace Number
16	交易時間	N	6	hhmmss，ex：142557
17	交易日期	N	8	YYYYMMDD，ex：20120620
18	RRN(調閱簽單碼)	N	12	RRN
19	授權碼	AN	6	6碼文數字，若授權失敗，則為空值
20	授權回覆碼	N	2	成功為00，其餘皆視為失敗(對應至輸出欄位的errcode，可參考「錯誤代碼一覽表」)
21	端末代碼	N	8	Terminal ID
22	特店代碼	N	15	Merchant ID
23	ECI
(對應6.11 POS規格)	N	2	Electronic Commerce Indicator(ECI)
VISA/JCB：05、06、07
MasterCard：02、01、00
24	XID
(3D認證交易編號)	ANS	20	XID
25	取消日期	N	8	YYYYMMDD，ex：20120620，若未取消訂單，則預設為8個0，ex：00000000
26	取消時間	N	9	hhmmssSSS，ex：213525314，若未取消訂單，則為空白
27	訂單狀態	N	1	請參考本手冊「訂單查詢輸出欄位」章節的欄位txstate
28	主機結帳日期	N	8	YYYYMMDD，若已請款結帳，ex：20120620，若未請款結帳，則預設為8個0，ex：00000000
29	主機結帳時間	N	9	hhmmssSSS，ex：213525314，若未請款結帳，則為空白
30	主機退貨結帳日期	N	8	YYYYMMDD，若已退貨結帳，ex：20120620，若未退貨結帳，則預設為8個0，ex：00000000
31	主機退貨結帳時間	N	9	hhmmssSSS，ex：213525314，若未退貨結帳，則為空白
32	轉入期限	N	8	YYYYMMDD，ex：20120710，此為訂單的轉入請款期限，若超過此日期未轉入，則訂單即無法轉入請款及結帳。
33	TID(交易識別碼)	AN	15	若係 VISA 卡跨國交易，此欄位存放VISA國際組織編制之Transaction ID。
34	PID(卡片級別識別碼)	AN	2	若係 VISA 卡跨國交易，此欄位存放VISA國際組織編制之Product ID。
35	VALIDCD(授權欄位驗證碼)	AN	4	若係 VISA 卡跨國交易，此欄位存放VISA國際組織編制之Validation Code。
36	PURCHCD(支付型態)	AN	1	紅利折抵交易放1，若非紅利折抵交易放空白。
37	REDEEMAMT(紅利抵扣後之金額)	N	9	紅利抵扣後之金額，最後兩碼為小數兩位固定值00，其餘右靠左補0
38	欄位58	AN	100	保留欄位
39	交易類型	N	1	0-一般交易；1-分期交易；2-紅利交易
40	分期期數	N	2	分期期數
41	首期分期金額	N	10	首期分期金額
42	其他每期金額	N	10	其他每期金額
43	次特店代號	N	8	次特店代號
44	次特店回覆碼	AN	4	次特店回覆碼
45	VE訊息狀態	AN	1	VE訊息狀態
46	PA訊息狀態	AN	4	PA訊息狀態
47	MPI回覆碼	AN	4	MPI回覆碼
48	累積退貨金額	N	12	累積退貨金額
49	PF ID	N	11	Payment Facilitator ID
50	保留欄位			保留欄位
51	保留欄位			保留欄位
52	保留欄位			保留欄位
53	保留欄位			保留欄位
54	保留欄位			保留欄位
55	處理主機	ANS	34	處理主機
56	退貨交易識別碼	AN	15	退貨TID
57	退貨授權碼	AN	6	退貨授權碼
58	M/C DS交易識別碼	ANS	36	MasterCard DS Transaction ID
59	清算識別碼	N	1	SETLFG
60	發卡行	N	3	發卡行代碼
61	卡別	A	1	卡別V、M、J
62	保留欄位			保留欄位
63	客製化頁面	N	1	客製化頁面
64	API交易識別碼	N	1	API交易識別碼
65	3D交易版本	N	1	3D交易版本
66	3DS 2.0交易結果	AN	1	3DS 2.0交易結果
67	3DS 2.0交易識別碼	ANS	36	3DS 2.0 Server TRANS_ID
68	Visa (token program indicator)	AN	1	Visa:token program indicator
69	MOTO交易	A	1	MOTO交易，是(Y)、否(N)
70	RECURRING交易	A	1	Recurring交易，是(Y)、否(N)
71	M/C MIT CIT	AN	4	MasterCard MIT/CIT


4.3 銀聯卡訂單查詢匯出xls格式
欄位序號	欄位名稱	型式	長度	說明
01	訂單編號	AN	32	訂單編號
02	取消/退貨日期	N	8	日期格式YYYYMMDD(西元年+月份+日期)
03	取消/退貨時間	N	9	時間格式hhmmssSSS(時+分+秒+毫秒)
04	異動日期	N	8	日期格式YYYYMMDD(西元年+月份+日期)
05	異動時間	N	9	時間格式hhmmssSSS(時+分+秒+毫秒) 
06	清算日期	N	8	日期格式YYYYMMDD(西元年+月份+日期)
07	回應日期	N	8	日期格式YYYYMMDD(西元年+月份+日期)
08	回應時間	N	6	時間格式hhmmss(時+分+秒) 
09	交易回覆碼	AN	2	交易回覆碼
10	回傳網址	ANS	512	回傳網址
11	收單行代碼	N	3	收單行代碼
12	交易金額(台幣)	N	12	左側未補0，右側仍保留小數兩位固定值00，ex：79100(實際金額791)
13	取消/退貨金額(台幣)	N	12	左側未補0，右側仍保留小數兩位固定值00，ex：79100(實際金額791)
14	交易回覆說明	ANS	1024	交易回覆說明
15	特店代碼	N	15	Merchant ID
16	端末代碼	N	8	Terminal ID
17	特店名稱	ANS	25	
18	卡號	N	19	
19	負向交易狀態	ANS	30	CURR_NOTE
20	訂單狀態	AN	1	
21	交易類型	N	2	
22	交易追蹤碼	N	6	TRACE NUMBER
23	系統退貨/取消交易流水號	ANS	36	取消/退貨UUID
24	特店IP	ANS	40	
25	收單機構代碼	AN	11	ACQ CODE
26	行業類別碼	N	4	
27	QID(銀聯交易流水號)	ANS	21	
28	出檔日期	N	8	日期格式YYYYMMDD
29	原銀聯交易流水號	ANS	21	
30	原交易日期	N	8	日期格式YYYYMMDD
31	原交易系統追蹤號	N	6	ORIG TRACE NUMBER
32	原交易清算日期	N	8	日期格式YYYYMMDD
33	實際退貨日期	N	8	日期格式YYYYMMDD
34	處理主機	AN	20	
35	保留欄位			
36	銀聯交易版本	ANS	6	
37	銀聯後台通知日期	N	8	日期格式YYYYMMDD
38	銀聯後台通知時間	N	9	時間格式hhmmssSSS(時+分+秒+毫秒)

5.錯誤代碼一覽表
(僅供特店、銀行與本公司異常交易參考)
5.1網路收單授權交易回應代碼
Status	ErrorCode	DefinedType	Note
4		收單系統 Reject/Error
	99	ERR_RESPONSE_TIMEOUT	交易逾時回應訊息，通常為3D交易
	Others	SYSTEM ERROR	Reserved

※Status=8的異常交易，網路商家/持卡人，如需要請向收單銀行或發卡銀行了解原因※

Status	ErrorCode	DefinedType	Note
8：
發卡行或CARD回覆		Issuer/Acquirer Bank Response
	01	REFER TO CARD ISSUER	請持卡者與發卡銀行聯絡有關網路交易授權失敗的原因
	02	REFER TO CARD ISSUER	請持卡者與發卡銀行聯絡有關網路交易授權失敗的原因
	03	INVALID MERCHANT	未經授權使用的Merchant ID
	04	PICK UP-Card Closed	發卡行回覆拒絕交易
	05	Do not Honor	發卡行卡片資料驗證失敗
	06	Error	發卡行拒絕交易
	07	CAPTURE CARD	已報失卡，請與發卡銀行聯絡
	08	RESERVED TO ISO USE	
	09	RESERVED TO ISO USE	
	10	RESERVED TO ISO USE	
	11	RESERVED TO ISO USE	
	12	INVALID TRANSACTION	無效的交易，無法識別交易資料
	13	INVALID AMOUNT	無效的金額，無法識別消費金額
	14	INVALID CARD NUMBER	無效的卡號資料
	15	INVALID ISSUER	無效的發卡銀行
	16	RESERVED TO ISO USE	
	17	RESERVED TO ISO USE	
	18	RESERVED TO ISO USE	
	19	RE-ENTER TRANSACTION	銀行系統拒絕重複的交易
	20	INVALID RESPONSE	Reserved
	30	TIMEOUT/ FORMAT ERROR	逾時未回應/系統無法判別交易的資料格式
	31	Bank not supported by switch	交換中心不支援此卡號
	32	COMPLETED PARTIALLY	Reserved, completed partially
	33	EXPIRED CARD	發卡銀行：您的信用卡有效期限輸入錯誤
	34	SUSPECTED FRAUD	Reserved, suspected fraud故意欺騙
	35	CALL HELP	Reserved, card acceptor call acquirer security
	36	RESTRICTED CARD	Reserved
	37	CALL HELP	Reserved, card acceptor call acquirer security
	38	PIN TRY LOCK	Reserved, allowable PIN tries exceeded
	39	NO CREDIT ACCOUNT	系統無法獲得持卡者的信用資料
	40	REQUEST NOT SUPPORTED	Reserved, requested function not supported
	41	LOST CARD	信用卡已掛失
	42	NO UNIVERSAL ACCOUNT	Reserved, no universal account
	43	STOLEN CARD	失竊卡
	44	NO INVESTMENT ACCOUN	Reserved, no investment account
	45	RESERVED TO ISO USE	
	46	RESERVED TO ISO USE	
	47	RESERVED TO ISO USE	
	48	RESERVED TO ISO USE	
	49	RESERVED TO ISO USE	
	50	RESERVED TO ISO USE	
	51	NO SUFFICIENT FUNDS	發卡銀行：消費額度不足
	52	RESERVED TO ISO USE	
	54	CARD NOT OPEN/EXPIRED	信用卡有效期限過期, 發卡行回覆拒絕交易
	55	INCORRECT PIN	Reserved, incorrect personal identification number
	56	NO CARD RECORD	系統無法獲得持卡者的信用卡紀錄
	57	TRANS. NOT PERMITTED TO CARDHOLD	拒絕持卡者進行該網路交易/超過商店額度限制
	58	TRANS. NOT PERMITTED TO TERMINAL	拒絕商家進行該網路交易
	59	SUSPECTED CARD	嫌疑卡
	60	CALL HELP	Reserved, card acceptor call acquirer
	61	EXCCEEDS WITHDRAW	Reserved, amount too high
	62	RESTRICTED CARD	Reserved, card have to check
	63	CVC ERROR	信用卡安全識別碼錯誤, security violation
	64	ORIGINAL AMOUNT INCORRECT	相關交易的金額前後不符
	65	EXCCEEDS WITHDRAW	Reserved, exceeds withdrawal frequency limit
	66	CALL HELP	Reserved, card acceptor call acquirer’s security department
	67	HARD CAPTURE	Reserved, requires that card be picked up at ATM
	68	RESPONSE RECEIVED TOO LATE	Reserved
	69	RESERVED TO ISO USE	
	70	RESERVED TO ISO USE	
	71	RESERVED TO ISO USE	
	72	RESERVED TO ISO USE	
	73	RESERVED TO ISO USE	
	74	RESERVED TO ISO USE	
	75	PIN TRIES EXCEES	Reserved, pin try too many times
	76	RESERVED TO ISO USE	
	77	RESERVED TO ISO USE	
	78	RESERVED TO ISO USE	
	79	RESERVED TO ISO USE	
	80	RESERVED TO ISO USE	
	81	RESERVED TO ISO USE	
	82	RESERVED TO ISO USE	
	83	RESERVED TO ISO USE	
	84	RESERVED TO ISO USE	
	85	RESERVED TO ISO USE	
	86	RESERVED TO ISO USE	
	87	RESERVED TO ISO USE	
	88	RESERVED TO ISO USE	
	89	INVALID TERMINAL	Reserved,未經授權使用的Terminal ID
	90	SYSTEM NOT AVAILABLE	Reserved, cutoff is in process, transaction can be sent again in a few miniutes
	91	ISSUER INOPERATIVE	Reserved, issuer or switch center is inoperative
	92	NETWORK ROUTING ERROR	Reserved, financial institution or intermediate net. facility cannot be found for routing
	93	VIOLATION OF LAW	Reserved, transaction cannot be completed
	94	DUPLICATE TRANSMISSION	Reserved
	95	RECONCILE ERROR	Reserved, batch upload started
	96	Financial institution or intermediate network facility cannot be found for routing	發卡行無回應
	N7	Decline for CVV2 failure(VISA)	CVV2錯誤(VISA卡片)
	I1	unsupport installment transaction type	分期交易不支援此交易類別
	I2	unsupport installment period	分期交易不支援此期數
	I3	unsupport card no	分期交易不支援此卡號
	I4	expired card	分期交易卡片過期
	I5	error card no	分期交易卡號錯誤
	I6	Installment not Actived	分期主機回應分期活動尚未啟用
	I7	original transaction cancled	分期交易原交易已取消
	I8	original transaction not found	分期交易找不到原交易
	I9	system or error	分期交易系統錯誤
	IA	unformat error	分期交易格式錯誤
	Others	Call Issuer Bank Service	Reserved

Status	ErrorCode	DefinedType	Note
12：
特店屬性驗證及呼叫 MPI之回應(ACS結果會透過MPI回傳)		3D Secure / Risk Constraint Error
	01	3DSECURE_PROCESSING	3D交易處理中
	10	3DSECURE_INVALID_CARDNUMBER	3D交易卡號檢核有誤
	11	3DSECURE_CAVVCHK_ERROR	銀行檢核交易3D驗證碼失敗
	21	3DSECURE_OVER_AUTHAMT	單日累積授權金額超過銀行P.G.上限
	22	3DSECURE_OVER_AUTHNUM	單日累積授權筆數超過銀行P.G.上限
	23	3DSECURE_OVER_SGL_AUTHAMT	單筆授權金額超過銀行P.G.上限
	24	3DSECURE_INACTIVE	銀行P.G.暫時停止特店交易
	25	3DSECURE_INVALID_CARDRANGE	銀行P.G.或特店目前不接受該類信用卡交易
	26	3DSECURE_IN_PANBLACKLIST	銀行P.G.或特店目前不接受該卡號交易
	27	3DSECURE_NO_MERDATA	銀行P.G.尚未收到特店的收單申請
	28	3DSECURE_INVALID_ATTRIBUTE	銀行P.G.尚未收到特店申請該項服務
	40	3DSECURE_PROCESS_ERROR	3D驗證失敗
	41	3DSECURE_DENIAL	1.	持卡人輸入3D密碼驗證錯誤
2.	財金公司檢查特店與交易屬性
(1)3D驗證過程檢核有誤
(2)不符合特店3D屬性
(3)依特店設定可接受卡片別(VMJC)限制條件檢核拒絕
(4)依特店設定可接受交易型態(一般、分期、紅利)別，限制條件檢核拒絕
(5)依特店設定交易限制條件(自系、自行、跨系、跨國)檢核拒絕
	42	3DSECURE_NONPARTICIPATING_PAN	持卡人未申請3D驗證服務
	43	3DSECURE_ENROLLEDCHK_PAN	無法判讀卡號所屬的3D發卡行資訊
	44	3DSECURE_PARES_NONE	無法收到3D發卡行的認證回應
	45	3DSECURE_PARES_UNKNOWN	無法判讀3D發卡行回應的認證資料
	99	3DSECURE_TIMEOUT	3D交易回應逾時
 
5.2 Merchant Plug-In MPI 交易回應代碼
Rcode	描述說明
0000	Authentication succeeded Transaction Status is Y
Authentication Attemps Transaction Status is A
	3xxx打頭均由財金定義之錯誤碼，其中31xx~35xx為focas mpi web使用，36xx~37xx由mpi RMI使用，38xx~39xx由mpi server使用，30xx保留。
3101	Mpi web連線異常
3102	Mpi web資料庫異常
3103	端未不存在
3104	未參與財金MPI共用平台
3105	交易逾時
3189	特店傳送資料來源完整性檢核有誤
3199	系統發生非預期exception事件
3201	特店資料欄位格式有誤
3202	卡號欄位格式有誤
3203	效期欄位(年)格式有誤
3203	效期欄位(月)格式有誤
3204	消費金額格式有誤
3205	授權端網址格式有誤
3301	特店資料欄位未帶
3302	卡號欄位未帶
3303	效期欄位(年)未帶
3304	效期欄位(月)未帶
3305	消費金額未帶
3306	授權端網址未帶
3601	財金RMI MPI連線至MPI SERVER異常
3602	財金RMI MPI資料庫異常
3603	交易逾時
3699	財金RMI MPI系統異常
3801	MPI SERVER與DS之間連線異常
3802	資料庫連線異常
3803	交易逾時
6001	RMI無法傳遞訊息到MPI
7801	DS沒有回應，MPI服務處理逾時
7802	MPI服務停止
	3DS 2.0交易之錯誤訊息
E101	Message Received Invalid
E102	Message Version Number Not Supported
E103	Sent Messages Limit Exceeded
E201	Required Data Element Missing
E202	Critical Message Extension Not Recognised
E203	Format of one or more Data Elements is Invalid according to the Specification
E204	Duplicate Data Element
E301	Transaction ID Not Recognised
E302	Data Decryption Failure
E303	Access Denied, Invalid Endpoint
E304	ISO Code Invalid
E305	Transaction data not valid
E306	Merchant Category Code (MCC) Not Valid for Payment System
E307	Serial Number not Valid
E402	Transaction Timed Out
E403	Transient System Failure
E404	Permanent System Failure
E405	System Connection Failure
2001	Card authentication failed
2002	Unknown Device
2003	Unsupported Device
2004	Exceeds authentication frequency limit
2005	Expired card
2006	Invalid card number
2007	Invalid transaction
2008	No Card record
2009	Security failure
2010	Stolen card
2011	Suspected fraud
2012	Transaction not permitted to cardholder
2013	Cardholder not enrolled in service
2014	Transaction timed out at the ACS
2015	Low confidence
2016	Medium confidence
2017	High confidence
2018	Very High confidence
2019	Exceeds ACS maximum challenges
2020	Non-Payment transaction not supported
2021	3RI transaction not supported
2022	ACS technical issue
2026	Authentication attempted but not performed by the cardholder
9901	Send Mreq to Auth occurred an error
9902	ACS Cres return an errorMessage
9999	Unknow error in 3dsrequestor

5.3信用卡訂單查詢回應代碼
Rcode	描述說明
0	查詢成功
1	merID/MerchantID/TerminalID輸入不完整
2	未指定訂單編號
3	未指定訂單金額
4	AuthResURL有誤
10	查不到符合條件的訂單資訊
11	符合條件的訂單資料超過一筆
15	Can't find this merch in term。
(請查驗網路收單特店維護欄位網路特店專用流水號是否與merID一致。)
5.4銀聯網路收單交易回應代碼
Rcode	描述說明
00	支付成功
01	交易異常，支付失敗。詳情請諮詢95516
02	您輸入的卡號無效，請確認後輸入
03	發卡銀行不支持，支付失敗
06	您的卡已經過期，請使用其他卡支付
11	您卡上的餘額不足
14	您的卡已過期或者是您輸入的有效期不正確，支付失敗
15	您輸入的銀行卡密碼有誤，支付失敗
20	您輸入的轉入卡卡號有誤，支付失敗
21	您輸入的手機號或CVN2有誤，支付失敗
25	原始交易查找失敗
30	報文格式錯誤
31	Merchant state incorrect. The payment is not completed within the order timeout。
36	交易金額超過網上銀行交易金額限制，支付失敗
39	您已連續多次輸入錯誤密碼
56	交易受限
71	交易無效，無法完成，支付失敗
80	內部錯誤
81	可疑報文
82	驗簽失敗
83	超時
84	訂單不存在
94	重複交易

6.台灣Pay QR Code主掃付款公版頁面規格訊息
6.1交易流程
買家於網路商店點選付款功能後，商店系統應依章節相關說明(以html語法中的hidden方式並傳送相關資料參數)，轉導址至網路收單共用系統進行後續授權作業，流程如下圖所示。
 

 

6.2網路商店QR Code付款頁面連線訊息
商店網站必須透過HTTPS協定之POST方式傳遞參數資料，網址如下：
	測試環境：
<form method="POST" 
action=https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/QR_PAGE/>
	營運環境：
<form method="POST" action=https://www.focas.fisc.com.tw/FOCAS_WEBPOS/QR_PAGE/>

共用系統於接收商店傳送交易，經持卡人使用App掃描QR Code付款後，進行交易授權處理，並依處理結果針對授權結果回傳網址(AuthResURL)傳送本節相關輸出參數資料，商店應接收共用系統輸出參數進行後續處理作業，相關參數規格說明如下表。
系統參數名稱	Fmt	Len	輸入	輸出	備註
AcqBank	N	3	M	M	收單行代碼
authAmt	N	1…10		M	授權金額(臺幣金額整數)，建議商店需依據訂單編號等資訊比對authAmt與原訂單purchAmt之一致性
authRespTime	N	14		M	前端頁面系統處理回應時間(YYYYMMDDHHMMSS)
AuthResURL	ANS	1…512	M		授權結果回傳網址
lidm	AN	1…16	M	M	交易訂單編號，且訂單編號不可重複編號
MerchantID	AN	15	M	M	收單銀行授權使用的特店代號(由收單銀行編製提供)
purchAmt	N	1…10	M		交易金額(臺幣金額整數)
reqToken	H	64	M		reqToken = SHA-256
(AcqBank&AuthResURL&lidm&MerchantID&purchAmt&TerminalID&驗證參數)
respCode	N	4		M	若respCode為0000表示成功交易
respToken	H	64		M	respToken = SHA-256
(AcqBank&authAmt&authRespTime&lidm&MerchantID&respCode&Srrn&TerminalID&txnDateLocal&txnTimeLocal&驗證參數)
Srrn	N	12		M	系統追蹤號碼
TerminalID	AN	8	M	M	收單銀行授權使用的機台代號(由收單銀行編製提供)
timeoutSecs	N	1…3	C		交易逾時秒數，最大值為600秒，且此欄位將搭配本平台系統時間計算逾時上限
txnDateLocal	N	8		M	交易日期(yyyymmdd)
txnTimeLocal	N	6		M	交易時間(HHMMSS)
M:必要欄位；O:選擇欄位；C:條件式欄位；
註一：[前置作業] 特店人員透過FOCAS WEB進行『參數驗證』設定驗證參數，以利計算reqToken 和respToken使用，且此驗證參數與信用卡授權交易共用，故商店的信用卡授權交易需一併啟用此驗證機制。
註二：公版付款頁面顯示QR Code圖檔的「QR Code效期」欄位預設為本平台系統時間加上十分鐘，若商店需指定效期之期限，則於請求訊息使用timeoutSecs欄位。
註三：商店超過十分鐘未收到授權輸出結果訊息，需主動發送網路商店QR Code付款結果查詢訊息以確認該筆訂單授權之結果。
 

6.3網路商店QR Code付款結果通知訊息
當商店有申請設定『網路商店QR Code主動發送通知網址』，則「網路商店QR Code付款結果通知訊息」之輸入欄位與「網路商店QR Code付款頁面連線訊息」輸出欄位一致，相關參數規格說明如下表。
系統參數名稱	Fmt	Len	輸入	備註
AcqBank	N	3	M	收單行代碼
authAmt	N	1…10	M	授權金額(臺幣金額整數)，建議商店需依據訂單編號等資訊比對authAmt與原訂單purchAmt之一致性
authRespTime	N	14	M	後端伺服器系統處理回應時間(YYYYMMDDHHMMSS)
lidm	AN	1…16	M	交易訂單編號
MerchantID	AN	15	M	收單銀行授權使用的特店代號(由收單銀行編製提供)
respCode	N	4	M	若respCode為0000表示成功交易
respToken	H	64	M	respToken = SHA-256
(AcqBank&authAmt&authRespTime&lidm&MerchantID&respCode&Srrn&TerminalID&txnDateLocal&txnTimeLocal&驗證參數)
Srrn	N	12	M	系統追蹤號碼
TerminalID	AN	8	M	收單銀行授權使用的機台代號(由收單銀行編製提供)
txnDateLocal	N	8	M	交易日期(yyyymmdd)
txnTimeLocal	N	6	M	交易時間(HHMMSS)
M:必要欄位；O:選擇欄位；C:條件式欄位；
 

6.4網路商店QR Code付款結果查詢訊息
商店網站必須透過HTTPS協定之POST方式傳遞參數資料，網址如下：
	測試環境：
<form method="POST" 
action=https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/QR_PAGE/orderInquery/>
	營運環境：
<form method="POST" action=https://www.focas.fisc.com.tw/FOCAS_WEBPOS/QR_PAGE/orderInquery/>
系統參數名稱	Fmt	Len	輸入	輸出	備註
AcqBank	N	3	M	M	收單行代碼
lidm	AN	1…16	M	M	交易訂單編號
MerchantID	AN	15	M	M	收單銀行授權使用的特店代號(由收單銀行編製提供)
multiRecords	ANS	1…999		C	當resCode為0時，輸出必帶此欄位。

單筆資料內容範例如下：
totalRecords=1;record0:respCode=0000,Srrn=************,txnDateLocal=********,txnTimeLocal=132210;

多筆資料內容範例如下：
totalRecords=2;record0:respCode=0000,Srrn=************,txnDateLocal=********,txnTimeLocal=132210;record1:respCode=0000,Srrn=************,txnDateLocal=********,txnTimeLocal=143522;
purchAmt	N	1…10	M	M	交易金額(臺幣金額整數)
reqToken	H	64	M		reqToken = SHA-256
(AcqBank&lidm&MerchantID&purchAmt&TerminalID&驗證參數)
resCode	N	1…2		M	訊息回應代碼
0:查詢成功
10:查不到符合條件之訂單資訊
respToken	H	64		M	當resCode為0時，則respToken = SHA-256
(AcqBank&lidm&MerchantID&multiRecords&purchAmt&resCode&systemDateTime&TerminalID&驗證參數)

當resCode為10時，則respToken = SHA-256
(AcqBank&lidm&MerchantID&purchAmt&resCode&systemDateTime&TerminalID&驗證參數)
systemDateTime	N	14		M	系統處理回應時間(YYYYMMDDHHMMSS)
TerminalID	AN	8	M	M	收單銀行授權使用的機台代號(由收單銀行編製提供)；當resCode為0時，輸出必帶此欄位
M:必要欄位；O:選擇欄位；C:條件式欄位；
附錄 A 網路交易驗證作業機制
一、URL網路特店交易驗證作業流程
1.	[前置作業] 特店人員透過FOCAS WEB進行『參數驗證』設定。(詳細操作請見『網路收單共用系統特店帳務管理手冊』)
2.	持卡人進行信用卡付款作業。
3.	特店依據REQ_TOKEN規格產生TOKEN值。
4.	將訂單資訊及REQ_TOKEN值透過持卡人的Browser轉送給FOCAS系統，要求取得授權網頁(PayPage)。
5.	FOCAS系統依據規格進行REQ_TOKEN檢核作業。
6.	FOCAS檢核成功後，回應授權網頁給持卡人。(若檢核失敗則回錯誤網頁—檢核失敗)
7.	持卡人輸入卡號、效期、CVV等資訊。
8.	透過FOCAS將交易送至發卡行進行交易授權作業。
9.	FOCAS系統收到發卡行授權回應結果，依據RESP_TOKEN規格產生TOKEN值。
10.	將授權結果及RESP_TOKEN透過持卡人Browser轉送給特店。
11.	特店依據規格進行RESP_TOKEN檢核作業，及比對訂單資訊內容是否正確。
12.	依據特店比對結果呈現至持卡人Browser。
 
二、TOKEN規格說明
1.	特店REQ_TOKEN規格說明：
REQ_TOKEN = SHA-256(訂單編號&交易金額&驗證參數&特店代號&端末代號&交易時間)

2.	財金RESP_TOKEN規格說明：
授權成功：
RESP_TOKEN = SHA-256(授權結果狀態&訂單編號&驗證參數&授權碼&交易回應時間&特店代號&端末代號)
授權失敗：
RESP_TOKEN = SHA-256(授權結果狀態&錯誤碼&訂單編號&驗證參數&交易回應時間&特店代號&端末代號)

3.	欄位說明 (詳見『3.3輸出入欄位列表』)：
	授權結果狀態 (status)：A,1
	訂單編號 (lidm)：AN,1-19
	交易金額 (purchAmt)：A,1-12 (無小數位)
	驗證參數 (由資料庫取得)：AN,16 (區分大小寫)
	特店代號 (MerchantID)：AN,15
	端末代號 (TerminalID)：AN,8
	交易REQ時間 (LocalDate+LocalTime )：N,14 (yyyymmddHHMMSS)
	交易RESP時間 (authRespTime)：N,14 (yyyymmddHHMMSS)
	錯 誤 碼 (errcode)：AN,2
	授 權 碼 (authCode)：AN,1-6

4.	範例：
特店購物車交易內容範例說明
4.1 特店產出reqToken資訊說明：
訂單編號
lidm	20131024T009	交易金額
authAmt	200
特店代號
MerchantID	950876543219001	端末代號
TerminalID	90010001
驗證參數	1qaz2wsx3edc4rfv	交易時間
LocalDate+LocalTime	20131024141500
公式：SHA-256(訂單編號&交易金額&驗證參數&特店代號&端末代號&交易時間) ，即SHA-256(20131024T009&200&1qaz2wsx3edc4rfv&950876543219001&90010001&20131024141500) ，透過上述資料產出的reqToken：『935D2F84CBEC53716F5EB643FCE88C4DB6807372061821373F98AA0A38A10C5F』，特店開發人員可利用上述資料進行產出TOKEN並比對是否一致。

4.2.授權交易成功，財金產出respToken資訊說明：
訂單編號
lidm	20131024T009	授權碼
authCode	887693
特店代號
MerchantID	950876543219001	端末代號
TerminalID	90010001
授權結果狀態status	0	交易回應時間
authRespTime	20131024141600
驗證參數	1qaz2wsx3edc4rfv		
公式：SHA-256(授權結果狀態&訂單編號&驗證參數&授權碼&交易回應時間&特店代號&端末代號) ，即SHA-256 (0&20131024T009&1qaz2wsx3edc4rfv&887693&20131024141600&950876543219001&90010001)，透過上述資料產出的respToken：『428729487F8BE0329DD08AB8CDFCE677BEA16B04298EA46F1EBFA4C1EA4146C1』，特店開發人員可利用上述資料進行產出TOKEN並比對是否一致。

4.3.授權交易失敗，財金產出RESP_TOKEN資訊說明：
訂單編號
lidm	20131024T009	錯誤碼
errcode	30
特店代號
MerchantID	950876543219001	端末代號
TerminalID	90010001
授權結果狀態
status	8	交易回應時間
authRespTime	20131024141600
驗證參數	1qaz2wsx3edc4rfv		
公式：SHA-256(授權結果狀態&錯誤碼&訂單編號&驗證參數&交易回應時間&特店代號&端末代號)，即SHA-256 (8&30&20131024T009&1qaz2wsx3edc4rfv&20131024141600&950876543219001&90010001) ，透過上述資料產出的respToken：『5B0FB1319A9ADADFB274199B6FEF815F3B3252EC86DE06A974B03499980A1CBB』，特店開發人員可利用上述資料進行產出TOKEN並比對是否一致。
 
三、跨境匯入交易作業說明
1.	跨境匯入網路電子支付交易：詳細說明，請參閱附件一『財金公司跨境匯入網路電子支付交易規格書』。
2.	跨境匯入O2O交易：詳細說明，請參閱附件二『財金公司跨境匯入O2O交易規格書』。
 
四、銀聯網路收單交易作業說明
1.	網路收單特店維護作業內，設定特店為參加『銀聯網路收單』。
2.	購物車上傳參數，請見章節『3.3.1授權交易輸入欄位』。
3.	啟用銀聯網路交易驗證作業機制時，要求TOKEN規格如下：
•	購物車上傳參數：reqToken = SHA-256(lidm&purchAmt&驗證參數&MerchantID&TerminalID)。
4.	回應結果頁參數，請見章節『3.3.2授權交易輸出欄位』。
5.	啟用銀聯網路交易驗證作業機制時，回應TOKEN規格如下：
•	授權結果之respToken = SHA-256(respCode&qid&pan&lidm&驗證參數&authRespTime&MerchantID&TerminalID) 。
6.	詳細TOKEN說明，請見附錄A二。
 
五、傳輸欄位加密處理機制
1.	[前置作業] 特店人員透過FOCAS WEB進行『參數驗證』設定驗證參數，以利計算加密演算法使用，且此驗證參數與信用卡授權交易共用，故商店的信用卡授權交易需一併啟用TOKEN驗證機制，詳見附錄A二說明。
2.	加密後的內容= AES GCM (驗證參數，HexToByte(input data))。(採UTF-8編碼)
例如：
Input data：886-990123456
Input data轉Hex String：3838362d393930313233343536
驗證參數：313233343536373839303132333435363738393031323334
IV：313233343536373839303132
AES GCM加密結果轉Hex String為『AB69384763D8BBBBA08C1EF837F18A66878DC122E5829B67820EFB9A1C』。

