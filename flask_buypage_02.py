# =============================================
# 信用卡付款表單產生器 (FOCAS 2.0 版本)
# 作者：您的名字
# 最後更新：2024-03-15
# 說明：這是一個使用 Flask 框架建立的簡單網頁應用程式，
#       用於產生 FOCAS 2.0 版本的信用卡付款表單。
# =============================================

# 導入必要的套件
from flask import Flask, request, render_template_string, redirect, url_for
import datetime
import json
import logging
from typing import Dict, Any

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='payment.log'
)

# 建立 Flask 應用程式實例
app = Flask(__name__)

# =============================================
# 重要設定區
# 注意：這些值需要根據您的實際情況修改
# =============================================
MERCHANT_ID = "006054564229001"  # 特店代號（請向銀行申請）
TERMINAL_ID = "90010001"         # 終端機代號（請向銀行申請）
MER_ID = "0545642"              # 商家自編代號，選填
MERCHANT_NAME = "財團法人基督教正道福音神學中心道"        # 特店名稱
AUTO_CAP = "1"                   # 自動請款設定（1:自動請款）
RETURN_URL = "https://www.focas-test.fisc.com.tw/FOCAS_Web/web/signin/merchant/"  # 交易結果回傳網址（測試用）在正式環境中，您需要將此網址改為實際的網域

# 用於儲存交易記錄（實際應用中應該使用資料庫）
transaction_records: Dict[str, Dict[str, Any]] = {}

# =============================================
# HTML 表單模板
# 說明：這是網頁的 HTML 結構，使用 Jinja2 模板語法
# =============================================
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>FOCAS 2.0 線上付款表單產生器</title>
    <!-- 加入 CSS 樣式讓頁面更美觀 -->
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        form { 
            margin: 20px 0; 
        }
        input[type="text"], 
        input[type="number"] { 
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        input[type="submit"]:hover {
            background-color: #45a049;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .note {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #4CAF50;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 標題 -->
        <h2>🔧 FOCAS 2.0 刷卡表單產生器</h2>
        
        <!-- 說明區塊 -->
        <div class="note">
            <h3>📝 使用說明</h3>
            <p>1. 輸入訂單編號和金額</p>
            <p>2. 點擊「產生付款表單」按鈕</p>
            <p>3. 複製產生的 HTML 程式碼到您的網站</p>
        </div>

        <!-- 輸入表單 -->
        <form method="POST">
            <div>
                <label>訂單編號（lidm）：</label>
                <input type="text" name="lidm" required 
                       placeholder="請輸入訂單編號">
            </div>
            <div>
                <label>金額（purchAmt）：</label>
                <input type="number" name="purchAmt" required 
                       placeholder="請輸入金額">
            </div>
            <div>
                <input type="submit" value="產生付款表單">
            </div>
        </form>

        <!-- 表單產生後的顯示區域 -->
        {% if form_generated %}
        <div class="note">
            <h3>✅ 表單已產生！</h3>
            <p>請複製以下 HTML 程式碼貼到您的網站：</p>
        </div>
        <textarea>{{ payment_form }}</textarea>
        
        <div class="note">
            <h3>📦 測試付款</h3>
            <p>您可以直接點擊以下按鈕進行測試：</p>
        </div>
        {{ auto_form|safe }}
        {% endif %}
    </div>
</body>
</html>
"""

# =============================================
# 交易結果處理函數
# =============================================
def save_transaction_record(transaction_data: Dict[str, Any]) -> None:
    """
    儲存交易記錄
    
    Args:
        transaction_data: 交易資料
    """
    # 使用訂單編號作為索引
    order_no = transaction_data.get('lidm')
    if order_no:
        transaction_records[order_no] = {
            'timestamp': datetime.datetime.now().isoformat(),
            'status': transaction_data.get('status', ''),
            'amount': transaction_data.get('purchAmt', ''),
            'auth_code': transaction_data.get('authCode', ''),
            'card_no': transaction_data.get('cardNo', ''),
            'response_code': transaction_data.get('responseCode', ''),
            'response_msg': transaction_data.get('responseMsg', ''),
            'raw_data': transaction_data
        }
        logging.info(f"交易記錄已儲存: {order_no}")

def verify_transaction(transaction_data: Dict[str, Any]) -> bool:
    """
    驗證交易結果
    
    Args:
        transaction_data: 交易資料
        
    Returns:
        bool: 驗證是否通過
    """
    try:
        # 檢查必要欄位
        required_fields = ['lidm', 'status', 'purchAmt', 'authCode']
        if not all(field in transaction_data for field in required_fields):
            logging.error("缺少必要欄位")
            return False
            
        # 檢查交易狀態
        if transaction_data['status'] != '0':  # 0 表示交易成功
            logging.error(f"交易失敗: {transaction_data.get('responseMsg', '')}")
            return False
            
        # 檢查金額是否相符
        # 注意：實際應用中應該與資料庫中的訂單金額比對
        # amount = transaction_records.get(transaction_data['lidm'], {}).get('amount')
        # if amount and str(amount) != str(transaction_data['purchAmt']):
        #     logging.error("金額不符")
        #     return False
            
        return True
        
    except Exception as e:
        logging.error(f"驗證過程發生錯誤: {str(e)}")
        return False

# =============================================
# 路由設定
# =============================================
@app.route("/", methods=["GET", "POST"])
def generate_form():
    """主要表單產生函數"""
    if request.method == "POST":
        lidm = request.form["lidm"]
        purchAmt = request.form["purchAmt"]

        # 產生付款表單 HTML
        payment_form = f"""
<form method="post" action="https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/online/">
  <input type="hidden" name="MerchantID" value="{MERCHANT_ID}">
  <input type="hidden" name="TerminalID" value="{TERMINAL_ID}">
  <input type="hidden" name="merID" value="{MER_ID}">
  <input type="hidden" name="MerchantName" value="{MERCHANT_NAME}">
  <input type="hidden" name="purchAmt" value="{purchAmt}">
  <input type="hidden" name="lidm" value="{lidm}">
  <input type="hidden" name="AutoCap" value="{AUTO_CAP}">
  <input type="hidden" name="ReturnURL" value="{RETURN_URL}">
  <input type="submit" name="submit" value="結帳">
</form>
        """.strip()

        return render_template_string(
            HTML_TEMPLATE,
            form_generated=True,
            payment_form=payment_form,
            auto_form=payment_form
        )

    return render_template_string(HTML_TEMPLATE, form_generated=False)

@app.route("/payment/result", methods=["POST"])
def payment_result():
    """
    處理交易結果回傳
    FOCAS 2.0 會將交易結果 POST 到此網址
    """
    try:
        # 取得交易結果資料
        transaction_data = request.form.to_dict()
        logging.info(f"收到交易結果: {json.dumps(transaction_data, ensure_ascii=False)}")
        
        # 驗證交易結果
        if verify_transaction(transaction_data):
            # 儲存交易記錄
            save_transaction_record(transaction_data)
            
            # 回傳成功頁面
            return render_template_string("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>交易結果</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .success { color: green; }
                        .error { color: red; }
                    </style>
                </head>
                <body>
                    <h2 class="success">✅ 交易成功</h2>
                    <p>訂單編號：{{ transaction_data.lidm }}</p>
                    <p>交易金額：{{ transaction_data.purchAmt }}</p>
                    <p>授權碼：{{ transaction_data.authCode }}</p>
                    <p>回應訊息：{{ transaction_data.responseMsg }}</p>
                </body>
                </html>
            """, transaction_data=transaction_data)
        else:
            # 回傳失敗頁面
            return render_template_string("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>交易結果</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .error { color: red; }
                    </style>
                </head>
                <body>
                    <h2 class="error">❌ 交易失敗</h2>
                    <p>訂單編號：{{ transaction_data.lidm }}</p>
                    <p>錯誤訊息：{{ transaction_data.responseMsg }}</p>
                </body>
                </html>
            """, transaction_data=transaction_data)
            
    except Exception as e:
        logging.error(f"處理交易結果時發生錯誤: {str(e)}")
        return "處理交易結果時發生錯誤", 500

@app.route("/payment/query/<order_no>")
def query_transaction(order_no: str):
    """
    查詢交易記錄
    
    Args:
        order_no: 訂單編號
    """
    transaction = transaction_records.get(order_no)
    if transaction:
        return json.dumps(transaction, ensure_ascii=False, indent=2)
    return "找不到交易記錄", 404

# =============================================
# 主程式入口
# 說明：當直接執行此檔案時啟動網頁伺服器
# =============================================
if __name__ == "__main__":
    # 啟動 Flask 開發伺服器
    # debug=True 表示開啟除錯模式，方便開發
    # 注意：正式環境請關閉除錯模式
    app.run(debug=True, host='0.0.0.0', port=6001)
    
# =============================================
# 使用說明：
# 1. 安裝必要套件：
#    pip install flask
#
# 2. 修改設定：
#    - 將 MERCHANT_ID 改為您的特店代號
#    - 將 TERMINAL_ID 改為您的終端機代號
#    - 將 MER_ID 改為您的特店代號
#    - 將 MERCHANT_NAME 改為您的特店名稱
#    - 將 RETURN_URL 改為您的實際網域
#
# 3. 執行程式：
#    python flask_buypage_02.py
#
# 4. 開啟瀏覽器：
#    訪問 http://localhost:6001
#
# 5. 交易結果查詢：
#    訪問 http://localhost:6001/payment/query/訂單編號
# =============================================



