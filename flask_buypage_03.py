# =============================================
# 多幣別信用卡付款表單產生器 (FOCAS 2.0 版本)
# 作者：您的名字
# 最後更新：2024-03-15
# 說明：這是一個使用 Flask 框架建立的簡單網頁應用程式，
#       用於產生 FOCAS 2.0 版本的多幣別信用卡付款表單。
# =============================================

# 導入必要的套件
from flask import Flask, request, render_template_string, redirect, url_for
import datetime
import json
import logging
from typing import Dict, Any

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='payment.log'
)

# 建立 Flask 應用程式實例
app = Flask(__name__)

# =============================================
# 重要設定區
# 注意：這些值需要根據您的實際情況修改
# =============================================
MERCHANT_ID = "006054564229001"  # 特店代號（請向銀行申請）
TERMINAL_ID = "90010001"         # 終端機代號（請向銀行申請）
MER_ID = "0545642"              # 商家自編代號，選填
MERCHANT_NAME = "財團法人基督教正道福音神學中心道"        # 特店名稱
AUTO_CAP = "1"                   # 自動請款設定（1:自動請款）
RETURN_URL = "https://www.focas-test.fisc.com.tw/FOCAS_Web/web/signin/merchant/"  # 交易結果回傳網址（測試用）

# 支援的幣別設定
SUPPORTED_CURRENCIES = {
    "TWD": {
        "name": "新台幣",
        "code": "901",
        "symbol": "NT$",
        "action_url": "https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/online/"
    },
    "USD": {
        "name": "美金",
        "code": "840",
        "symbol": "US$",
        "action_url": "https://www.focas-test.fisc.com.tw/FOCAS_WEBPOS/online/"
    },
    "CNY": {
        "name": "人民幣",
        "code": "156",
        "symbol": "¥",
        "action_url": "https://www.focas-test.fisc.com.tw/FOCAS_UPOP/upop/"  # 銀聯網路收單
    }
}

# 用於儲存交易記錄（實際應用中應該使用資料庫）
transaction_records: Dict[str, Dict[str, Any]] = {}

# =============================================
# HTML 表單模板
# =============================================
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>FOCAS 2.0 多幣別線上付款表單產生器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        form { 
            margin: 20px 0; 
        }
        input[type="text"], 
        input[type="number"],
        select { 
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        input[type="submit"]:hover {
            background-color: #45a049;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .note {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #4CAF50;
            margin: 10px 0;
        }
        .currency-info {
            background-color: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🌍 FOCAS 2.0 多幣別刷卡表單產生器</h2>
        
        <div class="note">
            <h3>📝 使用說明</h3>
            <p>1. 選擇交易幣別</p>
            <p>2. 輸入訂單編號和金額</p>
            <p>3. 點擊「產生付款表單」按鈕</p>
            <p>4. 複製產生的 HTML 程式碼到您的網站</p>
        </div>

        <div class="currency-info">
            <h3>💱 支援幣別</h3>
            <ul>
                <li>🇹🇼 新台幣 (TWD) - 信用卡網路收單</li>
                <li>🇺🇸 美金 (USD) - 信用卡網路收單</li>
                <li>🇨🇳 人民幣 (CNY) - 銀聯網路收單</li>
            </ul>
        </div>

        <form method="POST">
            <div>
                <label>交易幣別：</label>
                <select name="currency" required onchange="updateCurrencyInfo()">
                    <option value="">請選擇幣別</option>
                    {% for code, info in currencies.items() %}
                    <option value="{{ code }}" {% if selected_currency == code %}selected{% endif %}>
                        {{ info.symbol }} {{ info.name }} ({{ code }})
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label>訂單編號（lidm）：</label>
                <input type="text" name="lidm" required 
                       placeholder="請輸入訂單編號" value="{{ lidm or '' }}">
            </div>
            <div>
                <label>金額（purchAmt）：</label>
                <input type="number" name="purchAmt" required 
                       placeholder="請輸入金額" value="{{ purchAmt or '' }}">
            </div>
            <div>
                <input type="submit" value="產生付款表單">
            </div>
        </form>

        {% if form_generated %}
        <div class="note">
            <h3>✅ {{ selected_currency_info.name }}表單已產生！</h3>
            <p>幣別：{{ selected_currency_info.symbol }} {{ selected_currency_info.name }} ({{ selected_currency }})</p>
            <p>請複製以下 HTML 程式碼貼到您的網站：</p>
        </div>
        <textarea>{{ payment_form }}</textarea>
        
        <div class="note">
            <h3>📦 測試付款</h3>
            <p>您可以直接點擊以下按鈕進行測試：</p>
        </div>
        {{ auto_form|safe }}
        {% endif %}
    </div>

    <script>
        function updateCurrencyInfo() {
            // 可以在這裡加入幣別選擇後的額外處理
        }
    </script>
</body>
</html>
"""

# =============================================
# 交易結果處理函數
# =============================================
def save_transaction_record(transaction_data: Dict[str, Any]) -> None:
    """儲存交易記錄"""
    order_no = transaction_data.get('lidm')
    if order_no:
        transaction_records[order_no] = {
            'timestamp': datetime.datetime.now().isoformat(),
            'status': transaction_data.get('status', ''),
            'amount': transaction_data.get('purchAmt', ''),
            'currency': transaction_data.get('currency', ''),
            'auth_code': transaction_data.get('authCode', ''),
            'card_no': transaction_data.get('cardNo', ''),
            'response_code': transaction_data.get('responseCode', ''),
            'response_msg': transaction_data.get('responseMsg', ''),
            'raw_data': transaction_data
        }
        logging.info(f"交易記錄已儲存: {order_no}")

def verify_transaction(transaction_data: Dict[str, Any]) -> bool:
    """驗證交易結果"""
    try:
        required_fields = ['lidm', 'status', 'purchAmt', 'authCode']
        if not all(field in transaction_data for field in required_fields):
            logging.error("缺少必要欄位")
            return False
            
        if transaction_data['status'] != '0':
            logging.error(f"交易失敗: {transaction_data.get('responseMsg', '')}")
            return False
            
        return True
        
    except Exception as e:
        logging.error(f"驗證過程發生錯誤: {str(e)}")
        return False

# =============================================
# 路由設定
# =============================================
@app.route("/", methods=["GET", "POST"])
def generate_form():
    """主要表單產生函數"""
    if request.method == "POST":
        currency = request.form["currency"]
        lidm = request.form["lidm"]
        purchAmt = request.form["purchAmt"]
        
        # 檢查幣別是否支援
        if currency not in SUPPORTED_CURRENCIES:
            return "不支援的幣別", 400
        
        currency_info = SUPPORTED_CURRENCIES[currency]
        action_url = currency_info["action_url"]
        currency_code = currency_info["code"]
        
        # 根據幣別產生不同的表單
        if currency == "CNY":
            # 人民幣使用銀聯網路收單，需要額外參數
            payment_form = f"""
<form method="post" action="{action_url}">
  <input type="hidden" name="MerchantID" value="{MERCHANT_ID}">
  <input type="hidden" name="TerminalID" value="{TERMINAL_ID}">
  <input type="hidden" name="merID" value="{MER_ID}">
  <input type="hidden" name="MerchantName" value="{MERCHANT_NAME}">
  <input type="hidden" name="purchAmt" value="{purchAmt}">
  <input type="hidden" name="lidm" value="{lidm}">
  <input type="hidden" name="Currency" value="{currency_code}">
  <input type="hidden" name="AutoCap" value="{AUTO_CAP}">
  <input type="hidden" name="AuthResURL" value="{RETURN_URL}">
  <input type="hidden" name="lagSelect" value="0">
  <input type="submit" name="submit" value="銀聯支付 ({currency_info['symbol']}{purchAmt})">
</form>
            """.strip()
        else:
            # 台幣和美金使用一般信用卡網路收單
            payment_form = f"""
<form method="post" action="{action_url}">
  <input type="hidden" name="MerchantID" value="{MERCHANT_ID}">
  <input type="hidden" name="TerminalID" value="{TERMINAL_ID}">
  <input type="hidden" name="merID" value="{MER_ID}">
  <input type="hidden" name="MerchantName" value="{MERCHANT_NAME}">
  <input type="hidden" name="purchAmt" value="{purchAmt}">
  <input type="hidden" name="lidm" value="{lidm}">
  <input type="hidden" name="currency" value="{currency_code}">
  <input type="hidden" name="AutoCap" value="{AUTO_CAP}">
  <input type="hidden" name="AuthResURL" value="{RETURN_URL}">
  <input type="submit" name="submit" value="信用卡支付 ({currency_info['symbol']}{purchAmt})">
</form>
            """.strip()

        return render_template_string(
            HTML_TEMPLATE,
            currencies=SUPPORTED_CURRENCIES,
            form_generated=True,
            payment_form=payment_form,
            auto_form=payment_form,
            selected_currency=currency,
            selected_currency_info=currency_info,
            lidm=lidm,
            purchAmt=purchAmt
        )

    return render_template_string(
        HTML_TEMPLATE, 
        currencies=SUPPORTED_CURRENCIES,
        form_generated=False
    )

@app.route("/payment/result", methods=["POST"])
def payment_result():
    """處理交易結果回傳"""
    try:
        transaction_data = request.form.to_dict()
        logging.info(f"收到交易結果: {json.dumps(transaction_data, ensure_ascii=False)}")
        
        # 取得幣別資訊
        currency_code = transaction_data.get('currency', '901')
        currency_name = "未知幣別"
        currency_symbol = ""
        
        for code, info in SUPPORTED_CURRENCIES.items():
            if info['code'] == currency_code:
                currency_name = info['name']
                currency_symbol = info['symbol']
                break
        
        if verify_transaction(transaction_data):
            save_transaction_record(transaction_data)
            
            return render_template_string("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>交易結果</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .success { color: green; }
                        .error { color: red; }
                    </style>
                </head>
                <body>
                    <h2 class="success">✅ 交易成功</h2>
                    <p>訂單編號：{{ transaction_data.lidm }}</p>
                    <p>交易金額：{{ currency_symbol }}{{ transaction_data.purchAmt }}</p>
                    <p>交易幣別：{{ currency_name }}</p>
                    <p>授權碼：{{ transaction_data.authCode }}</p>
                    <p>回應訊息：{{ transaction_data.responseMsg }}</p>
                </body>
                </html>
            """, transaction_data=transaction_data, currency_symbol=currency_symbol, currency_name=currency_name)
        else:
            return render_template_string("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>交易結果</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .error { color: red; }
                    </style>
                </head>
                <body>
                    <h2 class="error">❌ 交易失敗</h2>
                    <p>訂單編號：{{ transaction_data.lidm }}</p>
                    <p>交易幣別：{{ currency_name }}</p>
                    <p>錯誤訊息：{{ transaction_data.responseMsg }}</p>
                </body>
                </html>
            """, transaction_data=transaction_data, currency_name=currency_name)
            
    except Exception as e:
        logging.error(f"處理交易結果時發生錯誤: {str(e)}")
        return "處理交易結果時發生錯誤", 500

@app.route("/payment/query/<order_no>")
def query_transaction(order_no: str):
    """查詢交易記錄"""
    transaction = transaction_records.get(order_no)
    if transaction:
        return json.dumps(transaction, ensure_ascii=False, indent=2)
    return "找不到交易記錄", 404

# =============================================
# 主程式入口
# =============================================
if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=6001)
