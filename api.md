根據文件，信用卡付款使用：
MTI: 0100
ProcessingCode: 003000
使用 HTTP POST 請求發送 XML 電文
測試環境：https://www.focas-test.fisc.com.tw/FOCAS_WS/API20/V1/FISCII/acctVerify
三、你需要準備的資料（向銀行或財金申請）
欄位	說明
cardNumber	消費者信用卡卡號
expiredDate	卡片效期（需用 AES 或 3DES 加密）
verifyCode	用金鑰對交易進行 hash 簽章
merchantId	你店家的代號，由收單行提供
terminalId	你設備的代號，由收單行提供
verifyKey	加密或簽章用的密鑰
附註：如何產生 verifyCode 和加密 expiredDate？
這部分屬於「加解密／數位簽章」：
使用 3DES 或 AES-GCM 加密卡片效期
使用 HMAC-SHA256 或其他方式產生 verifyCode
這需要你取得收單行提供的「驗證參數（金鑰）」後，才能實作。
如果你還沒這些資料，可以用模擬資料來開發，再向銀行申請實際測試環境。




