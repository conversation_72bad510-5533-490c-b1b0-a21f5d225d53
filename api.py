# 導入必要的套件
import requests  # 用於發送 HTTP 請求
import json     # 用於處理 JSON 資料
import datetime # 用於產生訂單編號
import hashlib  # 用於加密
import logging  # 用於記錄日誌
from typing import Dict, Any  # 用於型別提示

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='payment_test.log'  # 日誌檔案名稱
)

class PaymentAPITest:
    """信用卡支付 API 測試類別"""
    
    def __init__(self):
        # 測試環境的設定
        self.api_url = "https://測試環境網址"  # 請替換為實際的測試環境網址
        self.merchant_id = "您的特店代號"      # 請替換為您的測試特店代號
        self.api_key = "您的API金鑰"          # 請替換為您的測試API金鑰
        
        # 設定請求標頭
        self.headers = {
            "Content-Type": "application/json",
            "MerchantId": self.merchant_id,
            "ApiKey": self.api_key
        }
        
        # 測試卡號（從測試資料中取得）
        self.test_cards = {
            "success": "測試卡號1",  # 交易成功的測試卡號
            "fail": "測試卡號2",     # 交易失敗的測試卡號
            "expired": "測試卡號3"   # 過期卡號
        }

    def generate_order_no(self) -> str:
        """產生訂單編號
        
        Returns:
            str: 格式為 '年月日時分秒' 的訂單編號
        """
        return datetime.datetime.now().strftime("%Y%m%d%H%M%S")

    def encrypt_data(self, data: str) -> str:
        """加密資料（範例使用 SHA256，實際加密方式需依照 API 規格）
        
        Args:
            data (str): 要加密的資料
            
        Returns:
            str: 加密後的資料
        """
        return hashlib.sha256(data.encode()).hexdigest()

    def test_connection(self) -> Dict[str, Any]:
        """測試 API 連線
        
        Returns:
            Dict[str, Any]: 包含測試結果的字典
        """
        try:
            # 發送 GET 請求測試連線
            response = requests.get(
                f"{self.api_url}/test",  # 測試連線的端點
                headers=self.headers,
                timeout=10  # 設定超時時間為 10 秒
            )
            
            # 檢查回應狀態
            response.raise_for_status()
            
            # 記錄成功訊息
            logging.info("連線測試成功")
            return {
                "success": True,
                "message": "連線測試成功",
                "data": response.json()
            }
            
        except requests.exceptions.RequestException as e:
            # 記錄錯誤訊息
            logging.error(f"連線測試失敗: {str(e)}")
            return {
                "success": False,
                "message": f"連線測試失敗: {str(e)}",
                "data": None
            }

    def test_transaction(self, amount: float, card_number: str) -> Dict[str, Any]:
        """測試交易
        
        Args:
            amount (float): 交易金額
            card_number (str): 測試卡號
            
        Returns:
            Dict[str, Any]: 包含交易結果的字典
        """
        try:
            # 準備交易資料
            order_no = self.generate_order_no()
            transaction_data = {
                "MerchantId": self.merchant_id,
                "OrderNo": order_no,
                "Amount": amount,
                "CardNumber": card_number,
                "ExpiryDate": "1225",  # 卡片到期日（MMYY格式）
                "CVV": "123",          # 卡片驗證碼
                "Currency": "TWD",     # 交易幣別
                "TransactionType": "AUTH"  # 交易類型：授權
            }
            
            # 加密敏感資料（實際加密方式需依照 API 規格）
            transaction_data["CardNumber"] = self.encrypt_data(card_number)
            
            # 發送交易請求
            response = requests.post(
                f"{self.api_url}/transaction",  # 交易端點
                headers=self.headers,
                json=transaction_data,
                timeout=30  # 設定超時時間為 30 秒
            )
            
            # 檢查回應狀態
            response.raise_for_status()
            
            # 記錄交易結果
            logging.info(f"交易測試成功 - 訂單編號: {order_no}")
            return {
                "success": True,
                "message": "交易測試成功",
                "data": response.json(),
                "order_no": order_no
            }
            
        except requests.exceptions.RequestException as e:
            # 記錄錯誤訊息
            logging.error(f"交易測試失敗: {str(e)}")
            return {
                "success": False,
                "message": f"交易測試失敗: {str(e)}",
                "data": None,
                "order_no": order_no if 'order_no' in locals() else None
            }

    def run_all_tests(self):
        """執行所有測試案例"""
        # 測試連線
        print("=== 開始連線測試 ===")
        connection_result = self.test_connection()
        print(json.dumps(connection_result, indent=2, ensure_ascii=False))
        
        if not connection_result["success"]:
            print("連線測試失敗，停止後續測試")
            return
        
        # 測試成功交易
        print("\n=== 測試成功交易 ===")
        success_result = self.test_transaction(100, self.test_cards["success"])
        print(json.dumps(success_result, indent=2, ensure_ascii=False))
        
        # 測試失敗交易
        print("\n=== 測試失敗交易 ===")
        fail_result = self.test_transaction(100, self.test_cards["fail"])
        print(json.dumps(fail_result, indent=2, ensure_ascii=False))
        
        # 測試過期卡片
        print("\n=== 測試過期卡片 ===")
        expired_result = self.test_transaction(100, self.test_cards["expired"])
        print(json.dumps(expired_result, indent=2, ensure_ascii=False))

def main():
    """主程式"""
    try:
        # 建立測試實例
        tester = PaymentAPITest()
        
        # 執行所有測試
        tester.run_all_tests()
        
    except Exception as e:
        # 記錄未預期的錯誤
        logging.error(f"測試過程發生錯誤: {str(e)}")
        print(f"測試過程發生錯誤: {str(e)}")

if __name__ == "__main__":
    main()