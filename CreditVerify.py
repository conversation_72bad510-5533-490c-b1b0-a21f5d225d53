import requests

# FISC 測試 API URL（持卡人驗證範例）
url = "https://www.focas-test.fisc.com.tw/FOCAS_WS/API20/V1/FISCII/acctVerify"

# 模擬信用卡驗證 XML 結構
xml_data = '''<?xml version='1.0' encoding='UTF-8'?>
<CreditVerifyReq xmlns="http://www.focas.fisc.com.tw/FiscII/acctVerify">
  <mti>0100</mti>
  <cardNumber>****************</cardNumber>
  <processingCode>003001</processingCode>
  <amt>************</amt>
  <traceNumber>191025</traceNumber>
  <localTime>191025</localTime>
  <localDate>********</localDate>
  <posEntryMode>812</posEntryMode>
  <expiredDate>60101674DE3FAD13</expiredDate> <!-- 這是加密後效期，實際需加密處理 -->
  <acqBank>006</acqBank>
  <terminalId>********</terminalId>
  <merchantId>***************</merchantId>
  <otherInfo>{"tag90":"1C667D63..."}</otherInfo>
  <verifyCode>ABCD1234567890...FAKEHASH</verifyCode>
</CreditVerifyReq>'''

# 設定標頭
headers = {
    'Content-Type': 'application/xml',
}

# 發送 POST 請求
response = requests.post(url, data=xml_data.encode('utf-8'), headers=headers)

# 顯示結果
print("狀態碼:", response.status_code)
print("回應內容:\n", response.text)
