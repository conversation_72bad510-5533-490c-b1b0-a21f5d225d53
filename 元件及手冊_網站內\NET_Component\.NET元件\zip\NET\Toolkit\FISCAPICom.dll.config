﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <add key="publicKeyHex" value="" />
    <add key="version" value="" />
  </appSettings>


  <startup>

    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/>

  </startup>

  <log4net>

    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">

      <layout type="log4net.Layout.PatternLayout">

        <!--訊息格式 EX: 2016-03-01 18:15:59,912 [10] INFO  Log4netTest.Program - 9-->

        <conversionPattern value="%date [%thread] [%level] %class %method %logger - %message%newline"/>

      </layout>

    </appender>
    
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <!--設定檔案路徑與名稱-->
      <file value="D:\log\Focas.log"/>
      <staticLogFileName value="true"/>
      <rollingStyle value="Date"/>
      <datePattern value=".yyyyMMdd"/>
      <layout type="log4net.Layout.PatternLayout">
      <!--印出 日期 Thread編號 class名稱 method名稱 Logger名稱 - [層級] 訊息-->
      <conversionPattern value="%date [%thread] [%level] %class %method %logger - %message%newline"/>
      </layout>
    </appender>

    <root>
      <!--Level為INFO的Log才會寫出-->
      <level value="DEBUG"/>
      <!--印到Console上-->
      <appender-ref ref="ConsoleAppender"/>
      <!--寫出至檔案-->
      <appender-ref ref="RollingFileAppender"/>
    </root>

  </log4net>

</configuration>
